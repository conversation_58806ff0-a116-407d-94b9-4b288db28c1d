function frmProfile_flxFirstName_setVisibility(boolean) {
    voltmx.print("### frmProfile_flxFirstName_setVisibility");

    function flxFirstName_setVisibility() {
        voltmx.print("### frmProfile_flxFirstName_setVisibility flxFirstName_setVisibility: " + boolean);
        frmProfile.flxFirstName.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flxFirstName_setVisibility, []);
}

function frmProfile_flxFullname_setVisibility(boolean) {
    voltmx.print("### frmProfile_flxFullname_setVisibility");

    function flxFullname_setVisibility() {
        voltmx.print("### frmProfile_flxFullname_setVisibility flxFullname_setVisibility: " + boolean);
        frmProfile.flxFullname.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flxFullname_setVisibility, []);
}

function frmProfile_flxLastName_setVisibility(boolean) {
    voltmx.print("### frmProfile_flxLastName_setVisibility");

    function flxLastName_setVisibility() {
        voltmx.print("### frmProfile_flxLastName_setVisibility flxLastName_setVisibility: " + boolean);
        frmProfile.flxLastName.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flxLastName_setVisibility, []);
}

function frmProfile_flxEmail_setVisibility(boolean) {
    voltmx.print("### frmProfile_flxEmail_setVisibility");

    function flxEmail_setVisibility() {
        voltmx.print("### frmProfile_flxEmail_setVisibility flxEmail_setVisibility: " + boolean);
        frmProfile.flxEmail.setVisibility(boolean);
    }
    voltmx.runOnMainThread(flxEmail_setVisibility, []);
}

function frmProfile_lblName_setVisibility(boolean) {
    voltmx.print("### frmProfile_lblName_setVisibility");

    function lblName_setVisibility() {
        voltmx.print("### frmProfile_lblName_setVisibility lblName_setVisibility: " + boolean);
        frmProfile.lblName.setVisibility(boolean);
    }
    voltmx.runOnMainThread(lblName_setVisibility, []);
}

function frmProfile_init() {
    voltmx.print("### frmProfile_init");
}

function frmProfile_preshow() {
    Analytics_logScreenView("profile");
    voltmx.print("### frmProfile_preshow");
}

function frmProfile_postshow() {
    voltmx.print("### frmProfile_postshow");
}
//Display Profile for all providers
function frmProfile_displayProfile(first_name, last_name, full_name, email, Profile_Pic) {
    frmProfile_clear();
    if (first_name !== undefined) frmProfile.lblName.text = first_name;
    else frmProfile_lblName_setVisibility(false);
    if (last_name !== undefined) frmProfile.lblLastName.text = last_name;
    else frmProfile_flxLastName_setVisibility(false);
    if (email !== undefined) frmProfile.lblEmail.text = email;
    else frmProfile_flxEmail_setVisibility(false);
    voltmx.print("tst@@@");
    if (full_name !== undefined) frmProfile.lblFullname.text = full_name;
    else frmProfile_flxFullname_setVisibility(false);
    voltmx.application.dismissLoadingScreen();
    voltmx.print("### frmProfile_displayProfile Profile_Pic: " + Profile_Pic);
    frmProfile.show();
    if (Profile_Pic !== undefined) frmProfile.imgProfilePicture.src = Profile_Pic;
}
//Clear the profile form
function frmProfile_clear() {
    frmProfile.lblName.text = "";
    frmProfile.lblLastName.text = "";
    frmProfile.lblEmail.text = "";
    frmProfile.lblFullname.text = "";
    frmProfile.lblSubHeader.text = "";
    frmProfile_flxFirstName_setVisibility(true);
    frmProfile_flxLastName_setVisibility(true);
}

function frmProfile_back() {
    frmFirstLogin.show();
}