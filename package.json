{"name": "redline-mobile-app", "version": "2.6.0", "description": "VoltMX Iris mobile application for law enforcement and parking management", "author": {"name": "Twyns", "email": "<EMAIL>"}, "dependencies": {"properties-parser": "0.3.1", "needle": "1.6.0", "xml2js": "0.4.17", "adm-zip": "0.5.1", "archiver": "1.3.0", "minimist": "1.2.5"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.8.0", "@types/node": "^18.0.0", "typescript": "^4.9.0"}, "scripts": {"build:android": "node build.js --platform android", "build:ios": "node build.js --platform iphone", "build:clean": "rm -rf binaries build dist", "lint": "eslint modules/**/*.js controllers/**/*.js --fix", "format": "prettier --write modules/**/*.js controllers/**/*.js", "type-check": "tsc --noEmit", "dev:setup": "npm install && npm run lint && npm run format"}, "keywords": ["voltmx", "iris", "mobile", "law-enforcement", "parking", "kony"], "engines": {"node": ">=14.0.0"}}