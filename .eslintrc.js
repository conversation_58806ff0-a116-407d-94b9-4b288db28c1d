module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
  ],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'script', // VoltMX uses script mode, not modules
  },
  globals: {
    // VoltMX global objects
    voltmx: 'readonly',
    kony: 'readonly', // Legacy Kony namespace
    
    // VoltMX APIs
    voltmxmp: 'readonly',
    
    // Common VoltMX widgets and APIs
    Form: 'readonly',
    FlexContainer: 'readonly',
    Button: 'readonly',
    Label: 'readonly',
    TextBox: 'readonly',
    TextArea: 'readonly',
    Image: 'readonly',
    Segment: 'readonly',
    Map: 'readonly',
    Camera: 'readonly',
    
    // VoltMX services
    VMXFoundry: 'readonly',
    
    // Android specific (when using native bindings)
    java: 'readonly',
    
    // Your custom global modules
    Global: 'readonly',
    Utility: 'readonly',
    Service: 'readonly',
    Analytics: 'readonly',
    CaseData: 'readonly',
    GPS: 'readonly',
    MapData: 'readonly',
    PrintLawEnforcement: 'readonly',
    PrintParking: 'readonly',
    SyncUtil: 'readonly',
    Validate: 'readonly',
    constants: 'readonly',
  },
  rules: {
    // Relaxed rules for VoltMX development
    'no-unused-vars': 'warn',
    'no-undef': 'warn',
    'no-console': 'off', // VoltMX uses voltmx.print() but console is also valid
    'no-global-assign': 'error',
    'no-implicit-globals': 'off',
    'prefer-const': 'warn',
    'no-var': 'off', // VoltMX still uses var in many places
    
    // Code style
    'indent': ['warn', 4],
    'quotes': ['warn', 'double'],
    'semi': ['warn', 'always'],
    'comma-dangle': ['warn', 'never'],
    
    // Best practices for VoltMX
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    
    // Allow function declarations to be hoisted (common in VoltMX)
    'no-use-before-define': ['error', { 'functions': false }],
  },
  overrides: [
    {
      files: ['modules/**/*.js'],
      rules: {
        // Module-specific rules
        'no-implicit-globals': 'error',
      }
    },
    {
      files: ['controllers/**/*.js'],
      rules: {
        // Controller-specific rules
        'no-unused-vars': 'off', // Controllers often have unused parameters
      }
    }
  ]
};
