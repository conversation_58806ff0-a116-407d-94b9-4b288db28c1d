{
  "recommendations": [
    // Essential JavaScript/Node.js extensions
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    
    // Code quality and formatting
    "streetsidesoftware.code-spell-checker",
    "editorconfig.editorconfig",
    "ms-vscode.vscode-json",
    
    // Git and version control
    "eamodio.gitlens",
    "mhutchie.git-graph",
    
    // File management and navigation
    "ms-vscode.vscode-file-utils",
    "alefragnani.bookmarks",
    "gruntfuggly.todo-tree",
    
    // Mobile development helpers
    "ms-vscode.vscode-android",
    "ms-vscode.vscode-ios-debug",
    
    // XML and properties file support
    "redhat.vscode-xml",
    "inu1255.easy-snippet",
    
    // Debugging and testing
    "ms-vscode.vscode-js-debug",
    "hbenl.vscode-test-explorer",
    
    // Productivity
    "ms-vscode.vscode-typescript-next",
    "christian-kohler.path-intellisense",
    "formulahendry.auto-rename-tag",
    
    // Theme and UI
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    
    // Documentation
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced"
  ]
}
