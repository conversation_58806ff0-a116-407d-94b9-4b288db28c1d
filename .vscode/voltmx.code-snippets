{"VoltMX Form Controller": {"prefix": "vmx-form-controller", "body": ["define(function() {", "    return {", "        init: function() {", "            voltmx.print('${1:FormName} Controller initialized');", "        },", "", "        preShow: function() {", "            // Pre-show logic here", "            ${2}", "        },", "", "        postShow: function() {", "            // Post-show logic here", "            ${3}", "        },", "", "        onHide: function() {", "            // On hide logic here", "            ${4}", "        },", "", "        onDestroy: function() {", "            // Cleanup logic here", "            ${5}", "        }", "    };", "});"], "description": "Create a VoltMX form controller template"}, "VoltMX Module": {"prefix": "vmx-module", "body": ["/**", " * ${1:ModuleName} Module", " * ${2:Description}", " */", "", "var ${1:ModuleName} = {", "    /**", "     * Initialize the module", "     */", "    init: function() {", "        voltmx.print('${1:ModuleName} module initialized');", "        ${3}", "    },", "", "    /**", "     * ${4:Method description}", "     * @param {*} ${5:param} - ${6:Parameter description}", "     * @returns {*} ${7:Return description}", "     */", "    ${8:methodName}: function(${5:param}) {", "        ${9}", "    }", "};", "", "// Export the module", "if (typeof module !== 'undefined' && module.exports) {", "    module.exports = ${1:ModuleName};", "}"], "description": "Create a VoltMX module template"}, "VoltMX Service Call": {"prefix": "vmx-service-call", "body": ["var serviceParams = {", "    ${1:paramName}: ${2:paramValue}", "};", "", "var httpHeaders = {};", "", "var serviceCallback = function(result) {", "    if (result.isSuccessful) {", "        voltmx.print('Service call successful');", "        var responseData = result.${3:responseKey};", "        ${4:// Handle success}", "    } else {", "        voltmx.print('Service call failed: ' + JSON.stringify(result));", "        ${5:// Handle error}", "    }", "};", "", "voltmx.net.invokeService('${6:serviceURL}', serviceParams, serviceCallback, httpHeaders);"], "description": "Create a VoltMX service call template"}, "VoltMX Alert": {"prefix": "vmx-alert", "body": ["var alertConfig = {", "    message: '${1:<PERSON><PERSON> message}',", "    alertType: constants.ALERT_TYPE_INFO,", "    alertTitle: '${2:Alert Title}',", "    yesLabel: '${3:OK}',", "    noLabel: '${4:Cancel}',", "    alertHandler: function(response) {", "        if (response === true) {", "            ${5:// Handle OK}", "        } else {", "            ${6:// Handle Cancel}", "        }", "    }", "};", "", "voltmx.ui.<PERSON>(alertConfig);"], "description": "Create a VoltMX alert dialog"}, "VoltMX GPS Location": {"prefix": "vmx-gps", "body": ["var locationOptions = {", "    enableHighAccuracy: true,", "    timeout: 30000,", "    maximumAge: 60000", "};", "", "var locationSuccess = function(position) {", "    var latitude = position.coords.latitude;", "    var longitude = position.coords.longitude;", "    var accuracy = position.coords.accuracy;", "    ", "    voltmx.print('Location: ' + latitude + ', ' + longitude + ' (±' + accuracy + 'm)');", "    ${1:// Handle location success}", "};", "", "var locationError = function(error) {", "    voltmx.print('Location error: ' + error.message);", "    ${2:// Handle location error}", "};", "", "voltmx.location.getCurrentPosition(locationSuccess, locationError, locationOptions);"], "description": "Get current GPS location in VoltMX"}, "VoltMX Camera Capture": {"prefix": "vmx-camera", "body": ["var cameraOptions = {", "    source: constants.CAMERA_SOURCE_CAMERA,", "    compressionLevel: 50,", "    onSuccess: function(rawBytes) {", "        voltmx.print('Photo captured successfully');", "        ${1:// Handle photo success}", "    },", "    onFailure: function(error) {", "        voltmx.print('Camera error: ' + JSON.stringify(error));", "        ${2:// Handle camera error}", "    },", "    onCancel: function() {", "        voltmx.print('Camera cancelled by user');", "        ${3:// Handle camera cancel}", "    }", "};", "", "voltmx.camera.capturePhoto(cameraOptions);"], "description": "Capture photo using VoltMX camera"}, "VoltMX Store Get/Set": {"prefix": "vmx-store", "body": ["// Store data", "voltmx.store.setItem('${1:key}', JSON.stringify(${2:data}));", "", "// Retrieve data", "var storedData = voltmx.store.getItem('${1:key}');", "if (storedData) {", "    var parsedData = JSON.parse(storedData);", "    ${3:// Use parsed data}", "} else {", "    ${4:// Handle no data found}", "}"], "description": "Store and retrieve data using VoltMX store"}}