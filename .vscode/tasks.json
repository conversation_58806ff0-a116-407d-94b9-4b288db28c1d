{"version": "2.0.0", "tasks": [{"label": "VoltMX: Build Android", "type": "shell", "command": "node", "args": ["build.js", "--platform", "android"], "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "VoltMX: Build iOS", "type": "shell", "command": "node", "args": ["build.js", "--platform", "iphone"], "group": {"kind": "build", "isDefault": false}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "VoltMX: Clean Build", "type": "shell", "command": "rm", "args": ["-rf", "binaries", "build", "dist"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Lint JavaScript Files", "type": "shell", "command": "npx", "args": ["eslint", "modules/**/*.js", "controllers/**/*.js", "--fix"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "Format Code", "type": "shell", "command": "npx", "args": ["prettier", "--write", "modules/**/*.js", "controllers/**/*.js"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}