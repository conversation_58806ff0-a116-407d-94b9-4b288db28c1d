// VoltMX Iris Type Definitions for better IntelliSense
// This file provides basic type definitions for VoltMX APIs

declare namespace voltmx {
  // Core VoltMX functions
  function print(message: any): void;
  function type(obj: any): string;
  
  // Application lifecycle
  namespace application {
    function getCurrentForm(): any;
    function getPreviousForm(): any;
    function setApplicationBehaviors(behaviors: any): void;
    function addApplicationCallbacks(callbacks: any): void;
  }
  
  // Location services
  namespace location {
    function getCurrentPosition(
      successCallback: (position: any) => void,
      errorCallback?: (error: any) => void,
      options?: any
    ): void;
    function watchPosition(
      successCallback: (position: any) => void,
      errorCallback?: (error: any) => void,
      options?: any
    ): number;
    function clearWatch(watchId: number): void;
  }
  
  // Camera
  namespace camera {
    function capturePhoto(options: any): void;
    function captureVideo(options: any): void;
  }
  
  // Database
  namespace db {
    function openDatabase(name: string, version?: string, displayName?: string, estimatedSize?: number): any;
    function transaction(database: any, callback: (tx: any) => void, errorCallback?: (error: any) => void): void;
  }
  
  // Network
  namespace net {
    function HttpRequest(): any;
    function invokeService(url: string, inputParams: any, callback: (result: any) => void): void;
  }
  
  // Store (Key-Value storage)
  namespace store {
    function getItem(key: string): string | null;
    function setItem(key: string, value: string): void;
    function removeItem(key: string): void;
    function clear(): void;
    function key(index: number): string | null;
    function length(): number;
  }
  
  // Timer
  namespace timer {
    function schedule(id: string, callback: () => void, delay: number, repeat: boolean): void;
    function cancel(id: string): void;
  }
  
  // UI utilities
  namespace ui {
    function Alert(basicConfig: any, pspConfig?: any): void;
    function Toast(basicConfig: any): void;
  }
  
  // Phone
  namespace phone {
    function dial(phoneNumber: string): void;
    function sendSMS(phoneNumber: string, message: string): void;
  }
  
  // I18N
  namespace i18n {
    function getLocalizedString(key: string): string;
    function getCurrentLocale(): string;
    function setCurrentLocale(locale: string): void;
  }
  
  // Crypto
  namespace crypto {
    function encrypt(algorithm: string, key: string, data: string): string;
    function decrypt(algorithm: string, key: string, encryptedData: string): string;
    function hash(algorithm: string, data: string): string;
  }
  
  // Theme
  namespace theme {
    function getCurrentTheme(): string;
    function setCurrentTheme(themeName: string): void;
  }
  
  // Constants
  namespace constants {
    const ANCHOR_DIRECTION_LEFT: string;
    const ANCHOR_DIRECTION_RIGHT: string;
    const ANCHOR_DIRECTION_TOP: string;
    const ANCHOR_DIRECTION_BOTTOM: string;
    
    const BREAKPOINT_MAX_VALUE: number;
    const BREAKPOINT_MIN_VALUE: number;
    
    const CAMERA_SOURCE_CAMERA: number;
    const CAMERA_SOURCE_GALLERY: number;
    
    const ENCODING_UTF8: string;
    const ENCODING_BASE64: string;
  }
}

// Legacy Kony namespace (for backward compatibility)
declare const kony: typeof voltmx;

// Global form and widget types
declare class Form {
  constructor(basicProperties: any, layoutProperties?: any, platformSpecificProperties?: any);
  show(): void;
  hide(): void;
  destroy(): void;
  add(...widgets: any[]): void;
  remove(widget: any): void;
}

declare class FlexContainer {
  constructor(basicProperties: any, layoutProperties?: any, platformSpecificProperties?: any);
  add(...widgets: any[]): void;
  remove(widget: any): void;
  removeAll(): void;
}

declare class Button {
  constructor(basicProperties: any, layoutProperties?: any, platformSpecificProperties?: any);
  text: string;
  onClick: () => void;
}

declare class Label {
  constructor(basicProperties: any, layoutProperties?: any, platformSpecificProperties?: any);
  text: string;
}

declare class TextBox {
  constructor(basicProperties: any, layoutProperties?: any, platformSpecificProperties?: any);
  text: string;
  placeholder: string;
  onTextChange: (widget: any, context: any) => void;
}

declare class Segment {
  constructor(basicProperties: any, layoutProperties?: any, platformSpecificProperties?: any);
  data: any[];
  selectedRowIndex: [number, number];
  onRowClick: (widget: any, sectionIndex: number, rowIndex: number) => void;
  setData(data: any[]): void;
  addAll(data: any[]): void;
  removeAll(): void;
}

// Android Java interop (when available)
declare namespace java {
  function import(className: string): any;
}

// Your custom global modules
declare const Global: any;
declare const Utility: any;
declare const Service: any;
declare const Analytics: any;
declare const CaseData: any;
declare const GPS: any;
declare const MapData: any;
declare const PrintLawEnforcement: any;
declare const PrintParking: any;
declare const SyncUtil: any;
declare const Validate: any;
declare const constants: any;
