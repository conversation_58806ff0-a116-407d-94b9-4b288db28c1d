# Redline Mobile App - VoltMX Iris Project

A VoltMX Iris mobile application for law enforcement and parking management.

## 🚀 Quick Start

### Prerequisites
- VoltMX Iris IDE
- Node.js (v14 or higher)
- Android SDK (for Android builds)
- Xcode (for iOS builds, macOS only)

### Setup Development Environment

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Setup development tools:**
   ```bash
   npm run dev:setup
   ```

3. **Install recommended VS Code extensions:**
   - Open VS Code
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on macOS)
   - Type "Extensions: Show Recommended Extensions"
   - Install all recommended extensions

## 📁 Project Structure

```
├── modules/                 # JavaScript modules and business logic
│   ├── Global.js           # Global variables and functions
│   ├── Service.js          # Service layer for API calls
│   ├── GPS.js              # GPS and location services
│   ├── CaseData.js         # Case management logic
│   └── ...
├── controllers/            # Form controllers
│   └── mobile/            # Mobile-specific controllers
├── forms/                  # UI forms and layouts
│   └── mobile/            # Mobile-specific forms
├── models/                 # Data models
├── resources/             # Images, fonts, and other assets
├── userwidgets/           # Custom widgets
├── themes/                # UI themes
└── buildAutomation/       # Build scripts and automation
```

## 🛠️ Development Commands

### Building
```bash
# Build for Android
npm run build:android

# Build for iOS
npm run build:ios

# Clean build artifacts
npm run build:clean
```

### Code Quality
```bash
# Lint JavaScript files
npm run lint

# Format code
npm run format

# Type checking
npm run type-check
```

## 📝 VS Code Features

### IntelliSense Support
- VoltMX API autocomplete
- Custom module path resolution
- Type definitions for better code completion

### Code Snippets
Use these prefixes in VS Code:
- `vmx-form-controller` - Create form controller template
- `vmx-module` - Create module template
- `vmx-service-call` - Service call template
- `vmx-alert` - Alert dialog template
- `vmx-gps` - GPS location template
- `vmx-camera` - Camera capture template
- `vmx-store` - Local storage template

### Tasks
Access via `Ctrl+Shift+P` → "Tasks: Run Task":
- VoltMX: Build Android
- VoltMX: Build iOS
- VoltMX: Clean Build
- Install Dependencies
- Lint JavaScript Files
- Format Code

## 🔧 Configuration Files

- **`.vscode/settings.json`** - VS Code workspace settings
- **`jsconfig.json`** - JavaScript project configuration
- **`.eslintrc.js`** - ESLint configuration for code quality
- **`.prettierrc`** - Code formatting rules
- **`types/voltmx.d.ts`** - TypeScript definitions for VoltMX APIs

## 📱 Platform-Specific Notes

### Android
- Minimum SDK: API 27 (Android 8.1)
- Target SDK: API 34 (Android 14.0)
- Permissions configured in `projectProperties.json`

### iOS
- Minimum iOS version: 14.0
- App icons configured for various sizes
- Provisioning profiles in `certificates/` directory

## 🔍 Debugging

### Console Logging
Use `voltmx.print()` instead of `console.log()` for VoltMX compatibility:
```javascript
voltmx.print("Debug message: " + JSON.stringify(data));
```

### Common Issues
1. **Module not found errors**: Check path mappings in `jsconfig.json`
2. **Build failures**: Ensure all certificates and provisioning profiles are valid
3. **GPS not working**: Check location permissions in `projectProperties.json`

## 🤝 Contributing

1. Follow the established code style (use Prettier)
2. Run linting before committing (`npm run lint`)
3. Test on both Android and iOS platforms
4. Update documentation for new features

## 📄 License

This project is proprietary software owned by Twyns.

## 📞 Support

For technical support, contact: <EMAIL>
