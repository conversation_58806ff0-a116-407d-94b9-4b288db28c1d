//#ifdef android
//Bluetooth globals
let BluetoothAdapter = java.import('android.bluetooth.BluetoothAdapter');
let KonyMain = java.import('com.konylabs.android.KonyMain');
let context1 = KonyMain.getActivityContext();
let BluetoothManager = java.import('android.bluetooth.BluetoothManager');
let Intent = java.import('android.content.Intent');
let PackageManager = java.import('android.content.pm.PackageManager');
let Context = java.import('android.content.Context');
let IntentFilter = java.import('android.content.IntentFilter');
let BluetoothDevice = java.import('android.bluetooth.BluetoothDevice');
let BluetoothGatt = java.import('android.bluetooth.BluetoothGatt');
let BluetoothGattCallback = java.import('android.bluetooth.BluetoothGattCallback');
let BluetoothGattCharacteristic = java.import('android.bluetooth.BluetoothGattCharacteristic');
let BluetoothGattDescriptor = java.import('android.bluetooth.BluetoothGattDescriptor');
let BluetoothGattService = java.import('android.bluetooth.BluetoothGattService');
let BluetoothProfile = java.import('android.bluetooth.BluetoothProfile');
let URI = java.import('android.net.Uri');
let UUID = java.import('java.util.UUID');
let File = java.import('java.io.File');
let ba = BluetoothAdapter.getDefaultAdapter();
if (ba != null) {
    bscanner = ba.getBluetoothLeScanner();
}
var i = 0;
var newSearchDevices = [];
let ResolveInfo = java.import('android.content.pm.ResolveInfo');
//Camera
let CameraNative = java.import('android.hardware.Camera');
//Firebase analytics
// Initialize Firebase Analytics instance
let FirebaseAnalytics = java.import('com.google.firebase.analytics.FirebaseAnalytics');
let Bundle = java.import('android.os.Bundle');
//#endif

//CouchDB globals
// localDB = {};
let remoteDB = {};
//couchDB functions

let Global = {};
Global.vars = {};

//Global_appService = function(){
function Global_appService(params) {
    voltmx.print("### appService ###");
    deepLink(params);
}
//globals
function Global_initGlobals() {
    voltmx.print("### Global_initGlobals() ###");
  	//#ifdef android
    Global.vars.firebaseAnalytics = FirebaseAnalytics.getInstance(KonyMain.getActivityContext().getApplication());
  	//#else
    var GoogleAnalyticsWrapper = objc.import("GoogleAnalyticsWrapper");
  	if (GoogleAnalyticsWrapper != null) {
      voltmx.print("### Global_initGlobals() ### appName: " + appConfig.appName);
      try {
        let AppConfigType = 1; // TwynsGen
        if (appConfig.appName.toLowerCase().includes("test")) {
          AppConfigType = 0; // TwynsTest
        }
        GoogleAnalyticsWrapper.setConfigType(AppConfigType);
        Global.vars.firebaseAnalyticsiOS = GoogleAnalyticsWrapper.sharedInstance();
        voltmx.print("### >> Global_initGlobals > GoogleAnalyticsWrapper initialized: << ###");
      } catch (error) {
        voltmx.print("### >> Global_initGlobals > GoogleAnalyticsWrapper error: << ###" + error);
      }
    }
    //#endif  
  	//BUILD FOR GLOBAL
    // GEN = General
    // RWS = Rijkswaterstaat
    // EPS = EPS
    // OV = Openbaar vervoer
    // NS = NS
  	Global.vars.buildFor = "GEN";
    Global.vars.buildForKonyEnvironment = "DEV";
    Global.vars.overideTeam100DoNotVersionCheck = false;
    //END
    Global.vars.environment = voltmx.store.getItem("gEnvironment");
    if (Global.vars.environment == null) {
        voltmx.print("### Global_initGlobals 1 Global.vars.environment: " + Global.vars.environment);
    	Global.vars.environment = "production";
    }
    voltmx.print("### Global_initGlobals 2 Global.vars.environment: " + Global.vars.environment);
    Global.vars.hostlocation = voltmx.store.getItem("gHostlocation");
    if (Global.vars.hostlocation === null) {
        voltmx.print("### Global_initGlobals 1 Global.vars.hostlocation: " + Global.vars.hostlocation);
  		Global.vars.hostlocation = "l_choose";
    }
    voltmx.print("### Global_initGlobals 2 Global.vars.hostlocation: " + Global.vars.hostlocation);
  	//transportticket globals - moet naar zaak
    Global.vars.transportticket = {
        amount: 0,
        fineAmount: 50,
        totalamount: 0
    };
    //menu
    if (Global.vars.buildFor == "RWS") {
        Global.vars.menudata = [
            //{imgSelector : "menuunselect.png", imgIcon : "menu_follow_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_followUp"), lblLineBottom : " "},
            //{imgSelector : "menuunselect.png", imgIcon : "menu_trace_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_trackDown"), lblLineBottom : " "},
            {
                imgSelector: "menuunselect.png",
                imgIcon: "menuregister.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_register"),
                lblLineBottom: " "
            }, {
                imgSelector: "menuunselect.png",
                imgIcon: "menuregister.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_registerconcept"),
                lblLineBottom: " "
            },
            //{imgSelector : "menuunselect.png", imgIcon : "menu_activecase_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_activeCases"), lblLineBottom : " "},
            //{imgSelector : "menuunselect.png", imgIcon : "menu_activecase_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_statistics"), lblLineBottom : " "},
            //{imgSelector : "menuunselect.png", imgIcon : "menu_history.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_history"), lblLineBottom : " "},
            {
                imgSelector: "menuunselect.png",
                imgIcon: "menuconcept.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_concepts"),
                lblLineBottom: " "
            }, {
                imgSelector: "menuunselect.png",
                imgIcon: "menuoutbox.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_outbox"),
                lblLineBottom: " "
            },
            //{imgSelector : "menuunselect.png", imgIcon : "menu_logon.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_login"), lblLineBottom : " "}
        ];
    } else {
        Global.vars.menudata = [
            //{imgSelector : "menuunselect.png", imgIcon : "menu_follow_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_followUp"), lblLineBottom : " "},
            {
                imgSelector: "menuunselect.png",
                imgIcon: "menu_trace_select.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_trackDown"),
                lblLineBottom: " "
            }, {
                imgSelector: "menuunselect.png",
                imgIcon: "menuregister.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_register"),
                lblLineBottom: " "
            },
            //{imgSelector : "menuunselect.png", imgIcon : "menuregister.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_registerconcept"), lblLineBottom : " "},
            //{imgSelector : "menuunselect.png", imgIcon : "menu_activecase_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_activeCases"), lblLineBottom : " "},
            //{imgSelector : "menuunselect.png", imgIcon : "menu_activecase_select.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_statistics"), lblLineBottom : " "},
            //{imgSelector : "menuunselect.png", imgIcon : "menu_history.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_history"), lblLineBottom : " "},
            {
                imgSelector: "menuunselect.png",
                imgIcon: "menuconcept.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_concepts"),
                lblLineBottom: " "
            }, {
                imgSelector: "menuunselect.png",
                imgIcon: "menuoutbox.png",
                lblMenuItem: voltmx.i18n.getLocalizedString("l_outbox"),
                lblLineBottom: " "
            }
            //{imgSelector : "menuunselect.png", imgIcon : "menu_logon.png", lblMenuItem : voltmx.i18n.getLocalizedString("l_login"), lblLineBottom : " "}
        ];
    }
    //encryption
    Global.vars.encryptionInUse = voltmx.store.getItem("globalInit");
    if (Global.vars.encryptionInUse === null) {
        Global.vars.encryptionInUse = false;
        voltmx.store.setItem("globalInit", Global.vars.encryptionInUse);
    }
    Global.vars.encryptionParams = [{
        environment: "development",
        passphrase: "4rao4r6T4rSR4ayr4ayh4byN4bi64bqt",
        vector: "4ZOJ4ZaT4Zma4ZaQ4ZCA4ZCG4ZGp4ZCE"
    }, {
        environment: "test",
        passphrase: "4oSI4ouz4oOB4p+IdA==",
        vector: "4ZOK4ZCC4ZCF4ZmZ4Ze64ZaW4ZGn4ZGp"
    }, {
        environment: "acceptance",
        passphrase: "4rao4r6T4rSu4puw4p+F4raS4qy8ZQ==",
        vector: "4ZaW4ZGp4ZSx4ZmZ4ZCA4ZaV4ZCC4Zma"
    }, {
        environment: "production",
        passphrase: "4oSI4ouz4oKd4oC44auG4amh4bK0Tg==",
        vector: "4ZaW4ZCB4ZOM4Zmc4ZGl4ZaS4ZCG4ZmZ"
    }, ];
    Global.vars.firstLogin = voltmx.store.getItem("firstLogin");
    if (Global.vars.firstLogin === null) {
        voltmx.print("### set first login to yes");
        Global.vars.firstLogin = "yes";
        Global.vars.encryptionInUse = true;
        voltmx.store.setItem("globalInit", Global.vars.encryptionInUse);
    }
    Global.vars.loggedOut = voltmx.store.getItem("loggedOut");
    voltmx.print("### Global_initGlobals Global.vars.loggedOut 1: " + Global.vars.loggedOut);
    if (Global.vars.loggedOut === null) {
        voltmx.print("### set loggedOut to yes");
        Global.vars.loggedOut = "yes";
        voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
    }
    voltmx.print("### Global_initGlobals Global.vars.loggedOut 2: " + Global.vars.loggedOut);
  //
    Global.vars.options = []; 
    //variables
    Global.vars.variables = [];
    Global.vars.optionvariablesText = "";
    Global.vars.optionvariablesSet = false;
    Global.vars.variableTimeField = "";
    Global.vars.selectedOption = {};
    Global.vars.filledOptionVariables = voltmx.store.getItem("filledOptionVariables");
    if (Global.vars.filledOptionVariables != null) {
        Global.vars.filledOptionVariables = JSON.parse(Global.vars.filledOptionVariables);
        voltmx.print("### Global Global.vars.filledOptionVariables: " + JSON.stringify(Global.vars.filledOptionVariables));
        voltmx.print("### Global Global.vars.filledOptionVariables length: " + Global.vars.filledOptionVariables.length);
    }
    Global.vars.copiedCompleteOptionText = voltmx.store.getItem("copiedCompleteOptionText");
    Global.vars.seeAndEditCompleteOptionTextEditTicket = false;
    Global.vars.readCompleteOptionText = false;
    // fillAndEditOptionDataEnabled
    Global.vars.fillAndEditOptionDataEnabled = false;
    Global.vars.fillAndEditOptionData = false;
    Global.vars.copyPasteCompleteOptionText = false;
    Global.vars.variablefieldFreeText = "";
    Global.vars.freeTextFieldmaxTextLength = 1000;
    Global.vars.toOptionsFromForm = "";
    Global.vars.toStatementFromForm = "";
    //users data
    Global.vars.gUsername = null;
    Global.vars.gEntraUsername = null;
    Global.vars.gInstanceId = null;
    Global.vars.gPassword = null;
    Global.vars.gDefaultDomain = "TWYNSPLATFORM";
  	Global.vars.gDomain = "";
    Global.vars.gIdentityProvider = null;
  	Global.vars.availableIDP = [];
  	Global.vars.gMyAuthToken = null;
    Global.vars.gCouchAuthToken = null;
    Global.vars.tokenExpirationTime = voltmx.store.getItem("tokenExpirationTime");
    Global.vars.gMyAuthTokenUsers = null;
    Global.vars.gMultiUsers = [];
    Global.vars.instances = voltmx.store.getItem("instances");
    Global.vars.profile = {
        full_name: "",
        u_id: "",
        p_p: null,
        first_name: "",
        last_name: "",
        email: ""
    };
    Global.vars.microsoftAccessToken = null;
    if (Global.vars.instances === null) {
        Global.vars.instances = [];
    }
    Global.vars.officerFunctions = voltmx.store.getItem("officerfunctions");
    Global.vars.instanceAuthorizations = voltmx.store.getItem("instanceAuthorizations");
    Global.vars.authorizedFunctions = ["NHA","BOA"];
  	Global.vars.connectedCloud = null;
    Global.vars.connectedCloudCaseType = null;
    Global.vars.multipleCloudsLogon = false; //instantie
    Global.vars.caseTypeDescription = "";
    Global.vars.gEmail = null;
    Global.vars.gFullName = null;
    Global.vars.gOfficerNumber = "";
    Global.vars.gTeamNumber = 0;
    Global.vars.gTeamName = "";
    Global.vars.gOfficerOath = "";
    Global.vars.gSecondOfficerId = null;
    Global.vars.gOfficerIdentification = null; //stamnummer
    Global.vars.gSecondOfficerName = "";
    Global.vars.gSecondOfficerNumber = "";
  	Global.vars.gOfficerToSelect = "";
    Global.vars.gTreatSecondAsFirst = "0";
    Global.vars.memberGroups = [];
    //Pincode
    Global.vars.usePin = voltmx.store.getItem("usePin");
    Global.vars.needToUsePin = true; //kan een parameter worden die naar instantieparameter kijkt of pin verplicht moet zijn
    Global.vars.UseTouchID = voltmx.store.getItem("UseTouchID");
    Global.vars.pinNumbers = "";
    Global.vars.createPinNumbers = "";
    Global.vars.verifyPinNumbers = "";
    Global.vars.resetPin = false;
    Global.vars.createPinInThisSession = false;
    Global.vars.createPinAfter = false;
    Global.vars.pinRetries = voltmx.store.getItem("pinRetries");
    if (Global.vars.pinRetries === null) {
        Global.vars.pinRetries = 0;
    }
    Global.vars.setServerInfoCount = 0;
    Global.vars.afterDeeplink = false;
    //multiUser device
    Global.vars.multiUserDevice = voltmx.store.getItem("MultiUserDevice"); // straks instantieparameter
    //Time
    Global.vars.setCurrentDateTimeCallback = null;
    //sync
    Global.vars.gSyncUsername = "4oGO4p2d4qWT4p6q4rSycg==";
    Global.vars.gSyncPassword = "4ou54a6o4qm94q604LeE4oGK4pqL4qWZ4rOw4omF4qWZZQ==";
    Global.vars.urlPrefix = voltmx.store.getItem("urlprefix");
    Global.vars.miniSync = false;
    Global.vars.planningsynced = false;
    Global.vars.planningLastSyncTime = null;
    Global.vars.planningSyncMinutes = 60;
    Global.vars.canceledNetworkConnection = false;
    Global.vars.authorizedCaseTypes = [];
    Global.vars.gProgressCount = 0;
    Global.vars.gProgress = 0;
    Global.vars.numberOfFailedSyncs = 0;
    Global.vars.objectReSync = false;
    // History
    Global.vars.numberdaysHistory = 365;
    Global.vars.numberdaysAppHistoryShown = 1;
    Global.vars.showHistoryPopup = true;
    Global.vars.showInHistoryPopup = [];
    Global.vars.disableExtraInfo = false;
    // Warning
    Global.vars.hideItemsForWarning = false;
    // registerForIdleTimeout RWS: 30
    Global.vars.registerForIdleTimeout = 720;
    //pouch
    Global.vars.pouchReplicateCompleted = false;
    //server info
    Global.vars.setServerInfoDone = false;
    Global.vars.setServerInfoCallBack = null;
  	Global.vars.auth_client = null;
    Global.vars.oAuthServiceProviders =  [{provider:"MobileAppAzureAD"}];
  	Global.vars.selectedOAuthServiceProvider = "MobileAppAzureAD";
    Global.vars.adalClientID = null;
    Global.vars.oAuthLogon = true;
    Global.vars.chooseEnvironment = true;
    Global.vars.environmentName = "";
    //scanner
    Global.vars.gScannerLight = "0";
    Global.vars.gScannerLightCallback = null;
    Global.vars.scanTimeOutSeconds = 5;
    //menu
    Global.vars.menusettingdata = [];
    Global.vars.useRouting = false;
    Global.vars.usePrinter = false;
    Global.vars.disablePrintButtons = true;
    Global.vars.backFromMenuSettings = false;
    Global.vars.openedFromMenu = false;
    Global.vars.openedFromResume = false;
    Global.vars.openedFromResumePreviousForm = null;
    Global.vars.previousMenuForm = null;
    // GEN Specific
    Global.vars.enableReadId = true;
    Global.vars.enablePersonOnResume = true;
    Global.vars.setPersonOnResume = false;
    Global.vars.personInputMethod = ""; // ReadId; ReadId-Manual; BvBSN; BvBSN-Manual; ReadId-BvBSN; ReadId-BvBSN-Manual; Manual
    //Teamselect
    Global.vars.selectedTeam = "";
    Global.vars.selectedTeamDescription = "";
    //AppModus
    Global.vars.appMode = voltmx.i18n.getLocalizedString("l_followUp");
    Global.vars.demoModus = false;
    Global.vars.processinfo_lastTaskProcessed = {};
    Global.vars.taskTypeId = "";
    Global.vars.taskType = "";
    Global.vars.taskTypeProcess = "";
    Global.vars.chosenTaskOutcome = "";
    Global.vars.defaultTaskOutcomeFrmResume = "afgehandeld_mulder"; //instantieparam
    Global.vars.defaultTaskOutcomeFrmFollow = "niet_relevant"; //instantieparam
    Global.vars.defaultTaskOutcomeFrmNHA = "afgehandeld_fiscaal"; //instantieparam
    Global.vars.defaultTaskOutcomeFrmFollowNoViolation = "afgehandeld_geen_overtr"; //instantieparam
    Global.vars.defaultTaskOutcomeFrmTrackDownNoViolation = "afgehandeld_geen_overtr"; //instantieparam
    Global.vars.defaultTaskOutcomeFrmTrackDownCancel = "niet_op_te_volgen_hh"; //instantieparam
    Global.vars.defaultTaskOutcomeRightsReadFalse = "annulering_geen_cautie"; //instantieparam
    Global.vars.defaultTaskOutcomeEditTicket = "appendTicketDone";
    Global.vars.defaultOutcomeForRemoveCase = null;
    // recheck
    Global.vars.defaultTaskOutcomeOtherPlate = "afgeh_ander_kenteken";
    Global.vars.defaultTaskOutcomeOtherLocation = "afgeh_andere_locatie";
    Global.vars.defaultTaskHandleCheckVehicle = "P98.08.T01";
    Global.vars.defaultTaskHandleCheckCard = "P99.10.T01";
    Global.vars.defaultTaskHandleCheckLocation = "P99.11.T01";
    Global.vars.useRecheckServiceARS = false; //instantieparam
    // 
    //
    Global.vars.onClickfrmTrackDownCancel = false;
    Global.vars.daysToRemoveConcepts = 14; //moet instantieparameter worden
    Global.vars.fileNamesForSendingOutbox = [];
    Global.vars.fileNamesForSendingOutboxNoCouch = [];
    Global.vars.fileNamesForSendingConceptsNoCouch = [];
    Global.vars.fileNamesForSendingConceptsToCouch = [];
    //printer
    Global.vars.gPrinter = {};
    Global.vars.defaultPrinter = "iMZ320";
    Global.vars.printHeadertext = "";
    Global.vars.printHeaderLogo = "";
    Global.vars.printDefaultLogo = "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";
    Global.vars.printFootertext = "";
    Global.vars.printPromptDefaultLanguage = "Engels";
    Global.vars.printPromptLanguage = "";
    Global.vars.printPromptLanguages = [{
            "prompt": [{
                "language": "Engels"
            }, {
                key: "languagecode",
                value: "en"
            }, {
                key: "l_nha",
                value: "Parking ticket and parking tax"
            }, {
                key: "l_location",
                value: "Street and number (at the height of)"
            }, {
                key: "l_vehicle",
                value: "Vehicle"
            }, {
                key: "l_brand",
                value: "Brand"
            }, {
                key: "l_assessmentNumber",
                value: "Ticket number"
            }, {
                key: "l_tax",
                value: "Amount parkingtax"
            }, {
                key: "l_addTax",
                value: "Additional costs"
            }, {
                key: "l_totalDue",
                value: "Total amount"
            }, {
                key: "l_notariff",
                value: "No tariff"
            }, {
                key: "l_datetime",
                value: "Date and time"
            }]
        },
        //       {"prompt" : [{"language": "Duits"},
        //                     {key : "languagecode", value :"de-DE"},
        //         			{key : "l_nha", value  : "Parkticket"},
        //                    	{key : "l_location", value  : "Standort"},
        //                    	{key : "l_vehicle", value  : "Fahrzeug"},
        //                     {key : "l_brand", value  : "Marke"},
        //         			{key : "l_assessmentNumber", value  : "Angriffsnummer"},
        //                     {key : "l_tax", value  : "Höhe der Parkgebühr"},
        //         			{key : "l_addTax", value  : "Zusätzliche Kosten"},
        //                     {key : "l_totalDue", value  : "Gesamtmenge"},
        //         			{key : "l_notariff", value  : "Kein Tarif"},
        //                     {key : "l_datetime", value  : "Datum und Uhrzeit"}
        //                    ]
        //       },
        {
            "prompt": [{
                "language": "Frans"
            }, {
                key: "languagecode",
                value: "fr-FR"
            }, {
                key: "l_nha",
                value: "Ammendes"
            }, {
                key: "l_location",
                value: "Nom de rue et numéro (à la hauteur de)"
            }, {
                key: "l_vehicle",
                value: "Véhicule"
            }, {
                key: "l_brand",
                value: "Marque"
            }, {
                key: "l_assessmentNumber",
                value: "Numéro d’ammendes"
            }, {
                key: "l_tax",
                value: "Montant parkingtax"
            }, {
                key: "l_addTax",
                value: "Coûts additionnels"
            }, {
                key: "l_totalDue",
                value: "Montant total"
            }, {
                key: "l_notariff",
                value: "Pas de tarif"
            }, {
                key: "l_datetime",
                value: "Date et heure"
            }]
        }, {
            "prompt": [{
                "language": "Nederlands"
            }, {
                key: "languagecode",
                value: "nl-NL"
            }, {
                key: "l_nha",
                value: "Naheffingsaanslag"
            }, {
                key: "l_location",
                value: "Straatnaam en huisnummer (ter hoogte van)"
            }, {
                key: "l_vehicle",
                value: "Voertuig"
            }, {
                key: "l_brand",
                value: "Merk"
            }, {
                key: "l_assessmentNumber",
                value: "Aanslagnummer"
            }, {
                key: "l_tax",
                value: "Bedrag parkeerbelasting"
            }, {
                key: "l_addTax",
                value: "Kosten naheffing"
            }, {
                key: "l_totalDue",
                value: "Totaal verschuldigd"
            }, {
                key: "l_notariff",
                value: "Geen tarief"
            }, {
                key: "l_datetime",
                value: "Datum en tijd"
            }]
        }
    ];
    // Print paramters
    Global.vars.plateLanguages = [];
    Global.vars.printPlateLanguage = false;
    Global.vars.initPlateLanguages = [{
        countrycode: 6030,
        country: 'NL',
        language: 'nl-NL'
    }, {
        countrycode: 9089,
        country: 'D',
        language: 'de-DE'
    }, {
        countrycode: 5002,
        country: 'F',
        language: 'fr-FR'
    }, {
        countrycode: 9999,
        country: 'DEF',
        language: 'nl-NL'
    }];
    //parameters
    Global.vars.instanceParameters = null;
  	Global.vars.offence = {};
    Global.vars.offenceCityParameters = null;
    //Services
    Global.vars.LanguageCode = "";
    Global.vars.mfRetry = 0;
    //Location globals
    Global.vars.gMunicipalSearchText = "";
    Global.vars.gCitiesSearchText = "";
    Global.vars.gStreetsSearchText = "";
    Global.vars.gZonesSearchText = "";
    Global.vars.gBack = false;
    Global.vars.backButtonText = "";
    Global.vars.gLocationCharacteristicPreviousId = [];
    Global.vars.gLocationCharacteristicBreadCrumb = [];
    Global.vars.gLocationCharacteristicMunicipalHeaderText = "";
    Global.vars.gSearchtype = "municipal";
    Global.vars.gMunicipalityCode = "";
    Global.vars.gCityCode = "";
    Global.vars.gStreetCode = "";
    Global.vars.gAreaCode = "";
    Global.vars.gZoneCode = "";
    Global.vars.gZone = "";
    Global.vars.gStreet = "";
    Global.vars.gCity = "";
    Global.vars.gMunicipality = "";
    Global.vars.gLatitude = 0.0;
    Global.vars.gLongitude = 0.0;
    Global.vars.gAltitude = 0.0;
    Global.vars.gHeading = 0;
    Global.vars.gSpeeding = 0;
    Global.vars.gGpsFix = false;
    Global.vars.lastStreetFromService = null;
    Global.vars.useBestProvider = false;
    Global.vars.gpsTimeoutCount = 0;
    Global.vars.streetDatbaseAlert = false;
    Global.vars.gStreetManual = false;
    Global.vars.gGpsRunning = false;
    Global.vars.TrackDownCaseDataLocationSet = false;
    Global.vars.gReverseGeoRunning = false;
    Global.vars.gStreetResult = false;
    Global.vars.keepLocationStreet = true;
    Global.vars.keepLocationHousenumber = true;
    Global.vars.locationDescriptionEnabled = false;
    Global.vars.gpsCallback = null;
    Global.vars.gpsCoordinatesCallback = null;
    Global.vars.numberOfStreetsToUpdate = 0;
    Global.vars.numberOfStreetsUpdated = 0;
    Global.vars.offenceMunicipalities = [];
    Global.vars.offenceCities = [];
    Global.vars.setDeviceLocationToCaseCallback = null;
    Global.vars.setDeviceLocationToRemoveCaseCallback = null;
    Global.vars.regions = [];
    Global.vars.watchID = null;
    Global.vars.watchMinimumTime = 1500;
    Global.vars.lastFoundRoutes = [];
    Global.vars.formHandleFollowMyLocationCircle = null;
    Global.vars.formHandleFollowMyLocation = false;
    Global.vars.hotspotRadiusLevels = [{
        name: "level5",
        radius: 500
    }, {
        name: "level4",
        radius: 250
    }, {
        name: "level3",
        radius: 100
    }, {
        name: "level2",
        radius: 50
    }, {
        name: "level1",
        radius: 10
    }]; //in meters
    Global.vars.maxBanDurations = [{
        max_duration: "1MONTH",
        duration: 1,
      	duration_in: "months"
    }, {
        max_duration: "2MONTH",
        duration: 2,
      	duration_in: "months"
    }, {
        max_duration: "2WEEK",
        duration: 2,
      	duration_in: "weeks"
    }, {
        max_duration: "3MONTH",
        duration: 3,
      	duration_in: "months"
    }, {
        max_duration: "4MONTH",
        duration: 4,
      	duration_in: "months"
    }, {
        max_duration: "4WEEK",
        duration: 4,
      	duration_in: "weeks"
    }, {
        max_duration: "5MONTH",
        duration: 5,
      	duration_in: "months"
    }, {
        max_duration: "6MONTH",
        duration: 6,
      	duration_in: "months"
    }]; //in months
    Global.vars.cloud_mode_control_k = "free"; //instantieparameter
    Global.vars.cloud_mode_control_o = "free"; //instantieparameter
    Global.vars.activeClouds = [];
    Global.vars.availableClouds = [];
    Global.vars.clampCloudId = null;
    Global.vars.triedGettingClampResults = false;
    Global.vars.noClampCasesFound = false;
    Global.vars.sortClamp = "DateDesc"; //instantieparameter
    Global.vars.sortUnClamp = "DateAsc"; //instantieparameter
    Global.vars.sortFiscaal = "DateDesc"; //instantieparameter
    Global.vars.sortLawEnforcement = "ElapsedTimeAsc"; //instantieparameter
    Global.vars.travelSpeed = 15; //in km/h //instantieparameter
    Global.vars.gpsWatchMyPositionCallback = null;
    Global.vars.lastSetCircle = "";
    Global.vars.timeSetCircle = false;
    Global.vars.licensplateHistoryRadius = "20";
    Global.vars.roadTypeValue = kindOfRoad.aRoad.value;
    Global.vars.roadTypeCode = kindOfRoad.aRoad.code;
    Global.vars.roadTypeDescription = "een weg";
    Global.vars.roadTypeFilter = []; //instanceparameter
    Global.vars.indUseHectometerMarkers = false; //instanceparameter
    Global.vars.hectometerRadius = 500; //TESTDATA //instanceparameter
    Global.vars.indmunicipalityrestricted = true; //instance
    Global.vars.userSelectEnabled = false;
    Global.vars.authorizedMunicipalities = [];
    Global.vars.roadTypes = [];
    Global.vars.specifyFurtherIndication = false;
    Global.vars.specifyFurtherIndicationRoadSide = false;
    Global.vars.specifyFurtherIndicationName = "";
    Global.vars.trainstationSelect = null;
    Global.vars.afkortingStation = "";
    Global.vars.stations = [];
    Global.vars.hectometerRaaiValidated = false;
    Global.vars.lastChosenFurtherIndicationKey = null;
    Global.vars.vehicleOwnerEnabled = false;
    Global.vars.historyEnabled = false;
    Global.vars.startInTrackDown = false;
    Global.vars.startInRegister = false;
    Global.vars.startInRegisterConcept = false;
    Global.vars.startInAddLabel = false;
    Global.vars.startInFollow = false;
    Global.vars.startInFollowPrefered = false;
    Global.vars.startInCardCheck = false;
    Global.vars.LocationCityCategory = "municipality";
    Global.vars.chosenMunicipality = null;
    //others
    Global.vars.LoggedIn = false;
    Global.vars.failedSynchronisation = false;
    //device info
    Global.vars.gDeviceInfo = voltmx.os.deviceInfo();
    //form globals
    Global.vars.previousForm = null;
    Global.vars.backToPreviousForm = null;
    Global.vars.casesMapTable = [];
    Global.vars.uploadText = false;
    //Countries
    Global.vars.gCountries = {
        showCount: 25,
        startRec: 0,
        endRec: 25,
        orderBy: "ORDER BY Description ASC",
        whereClause: ""
    };
    Global.vars.databaseLocale = "nl_NL"; // instantieparam = language_code
    Global.vars.CountryCode = 6030;
    Global.vars.CurrencyCode = "EUR";
    Global.vars.selectedCountryCode = null;
    Global.vars.selectedCountryScanable = false;
    //Service & insert
    Global.vars.status = 0;
    Global.vars.createCaseRetry = 0;
    Global.vars.retryUpdateCase = false;
    Global.vars.forceRefreshAfterUpgrade = false;
    Global.vars.travelMode = voltmx.store.getItem("travelMode");
    if (Global.vars.travelMode === null) {
        Global.vars.travelMode = "bicycle";
    }
    //Accelerometer
    Global.vars.accelerometerZ = null;
    //
    Global.vars.newAddressSet = false;
    Global.vars.newLocationSet = false;
    Global.vars.savedLocation = null;
    Global.vars.savedLocationManual = null;
    Global.vars.mediaSequence = 0;
    //Screen movement
    Global.vars.init_x = "0px";
    Global.vars.deviceInfo = voltmx.os.deviceInfo();
    Global.vars.gDeviceInfo = voltmx.os.deviceInfo();
    Global.vars.newLeft = 0;
    Global.vars.P_P_DP = Global.vars.deviceInfo.deviceWidth / Global.vars.deviceInfo.screenWidth;
    Global.vars.newTop = 0;
    Global.vars.P_H_DP = Global.vars.deviceInfo.deviceHeight / Global.vars.deviceInfo.screenHeight;
    Global.vars.headerTouchCounter = 0;
    Global.vars.firstTouchMain = false;
    Global.vars.firstLeft = null;
    Global.vars.firstTop = null;
    Global.vars.startx = null;
    Global.vars.starty = null;
    Global.vars.cannotMove = false;
    //
    Global.vars.pupNoConnectionCallback = {
        service: "",
        inputparam: {},
        recall: null
    };
    Global.vars.appRole = "";
    Global.vars.noFilter = 0;
    Global.vars.AreaTypeStreet = "";
    Global.vars.lastStartRow = null;
    //Follow globals
    //gLatitude = 0.0;
    //gLongitude = 0.0;
    gDestination = {};
    gRouteNames = [];
    gSetFilters = [];
    Global.vars.setCaseTypesToPopup = [];
    Global.vars.followCasesBookmark = null;
    //photos
    Global.vars.deleteFilePaths = [];
    Global.vars.attachments = [];
    Global.vars.newPhotos = [];
    Global.vars.deletePhotos = [];
    Global.vars.getPhotos = [];
    Global.vars.segPhotosData = [];
    Global.vars.photosLoaded = false;
    Global.vars.onlineAttachments = [];
    Global.vars.attachmentsLoaded = false;
    Global.vars.selectedPhoto = {};
    Global.vars.addPhotos = [];
    Global.vars.addANPRPhotos = [];
    Global.vars.showPhotosANPRLoaded = false;
    Global.vars.sendPhotosANPR = false;
    Global.vars.showMultimediaElement = true;
    //Offline function globals
    Global.vars.CaseDataFromFile = null;
    Global.vars.writtenCaseName = "";
    Global.vars.movedCase = "";
    Global.vars.couchCaseUpdateMultimediaCallback = null;
    Global.vars.frmTrackDefaultCaseTypeTaskType = {
        CaseType: "TRACK",
        TaskType: "Opsporen",
        StatusType: "CreateCase"
    };
    Global.vars.frmCheckVehicleDefaultCaseTypeTaskType = {
        CaseType: "CHECK_VEHICLE",
        TaskType: "RegisterCase",
        StatusType: "CreateCase"
    };
    Global.vars.frmCheckCardDefaultCaseTypeTaskType = {
        CaseType: "CHECK_CARD",
        TaskType: "RegisterCase",
        StatusType: "CreateCase"
    };
    Global.vars.frmCheckLocationDefaultCaseTypeTaskType = {
        CaseType: "CHECK_LOCATION",
        TaskType: "RegisterCase",
        StatusType: "CreateCase"
    };
    Global.vars.needToUploadPhotos = false;
    Global.vars.offlinePhotosToUpload = [];
    Global.vars.caseOpenedFromConcepts = false;
    Global.vars.selectedCaseIndex = null;
    Global.vars.numberOfConceptfiles = 0;
    Global.vars.numberOfConceptfilesOnDevice = 0;
    Global.vars.numberOfOutboxfiles = 0;
    Global.vars.numberOfopenTasks = 0;
    Global.vars.checkOpenTaskRunning = false;
    Global.vars.hideZeroCounters = false;
    Global.vars.showMenuCounter = true;
    Global.vars.saveCaseEnabled = true;
    Global.vars.fileNamesForSendingConceptsNoCouch = [];
    // AE0001
    Global.vars.noClickAreaHandleEnabled = true; // false for AE0001
    Global.vars.licensePlatePrefixEnabled = false; // true for AE0001
    Global.vars.recheckTotalPlateInfo = false; // true for AE0001
    //HandleCharacteristics globals
    Global.vars.handleCharacteristicType = "";
    Global.vars.handlingTypes = [];
    Global.vars.outcomeTypes = [];
    Global.vars.outcomeTypeCategories = [];
    Global.vars.caseTypes = [];
    Global.vars.caseTypeIds = "";
    Global.vars.previousCasetypes = {
        caseType: "",
        caseTypeId: ""
    };
    Global.vars.offenceTypeCodes = "";
    Global.vars.handleCharacteristicsBack = false;
    Global.vars.handleID = "";
    Global.vars.segRoadSignalsData = [];
    Global.vars.onStreetPayment = false;
    Global.vars.directPayAuthorized = false;
    Global.vars.administrationCosts = "0.00";
    Global.vars.printAmountDueWithAdminCosts = false;
    Global.vars.onStreetPaymentMethodValues = [{
        key: "1",
        value: "Contant"
    }, {
        key: "2",
        value: "Pin"
    }, {
        key: "3",
        value: "Pin en contant"
    }, ];
    //vehicle globals
    Global.vars.VehicleTypeModus = "";
    Global.vars.vehicleTypeGroupOriginal = {
        vehicleTypeGroup: "",
        vehicleTypeGroupDesc: "",
        vehicleTypeGroupId: ""
    };
    Global.vars.vehicleCountries = [];
    Global.vars.allCountries = [];
    Global.vars.licenseplateCountryCode = 6030;
    Global.vars.licenseplateCountryModule = "NL";
    Global.vars.licenseplateCountryDesc = "Nederland";
    Global.vars.licenseplateCountryEnableScan = true;
    Global.vars.licenseplateCountryRegisterCheck = true;
    Global.vars.lastChosenLicensePlateCountry = {
        licenseplateCountryDesc: "",
        licenseplateCountryModule: "",
        licenseplateCountryCode: null,
        licenseplateCountryEnableScan: false,
        licenseplateCountryRegisterCheck: false
    };
    Global.vars.unknownVehicleCountryLicense = {
        code: 0,
        description: voltmx.i18n.getLocalizedString("l_unknown"),
        module: voltmx.i18n.getLocalizedString("l_unknownModule")
    };
    Global.vars.unknownVehicleCountry = false;
    Global.vars.anprCalled = false;
    Global.vars.imagesReset = false;
    Global.vars.frmTrackDownANPRresult = [];
    Global.vars.frmCheckVehicleANPRresult = [];
    Global.vars.frmVehicleANPRresult = [];
    Global.vars.vehicleRegistrationCalled = false;
    Global.vars.getAllOffences = true;
    Global.vars.showVehicleScanButton = true;
    //
    Global.vars.btnCountryVehicleCode = false;
    Global.vars.preferences = {
        environments: [{
            name: "production",
            i18n: "l_production"
        }, {
            name: "acceptance",
            i18n: "l_acceptance"
        }, {
            name: "test",
            i18n: "l_test"
        }, {
            name: "development",
            i18n: "l_development"
        }],
        checktypes: [{
            name: "vehicle",
            i18n: "l_vehicle"
        }, {
            name: "signal",
            i18n: "l_signal"
        }, {
            name: "parking",
            i18n: "l_parking"
        }],
        lightOptions: [{
            action: "light_off",
            value: "1",
            lblMenuItem: voltmx.i18n.getLocalizedString("bt_lightoff")
        }, {
            action: "light_off",
            value: "2",
            lblMenuItem: voltmx.i18n.getLocalizedString("bt_lightoff_once")
        }, {
            action: "light_on",
            value: "3",
            lblMenuItem: voltmx.i18n.getLocalizedString("bt_lighton")
        }, {
            action: "light_on",
            value: "4",
            lblMenuItem: voltmx.i18n.getLocalizedString("bt_lighton_once")
        }],
        autoRestartEnabled: [{
            name: "yes",
            i18n: voltmx.i18n.getLocalizedString("l_yes")
        }, {
            name: "no",
            i18n: voltmx.i18n.getLocalizedString("l_no")
        }]
    };
    Global.vars.endpoints = [];
    Global.vars.appVersion = {
        version: "1.3.31",
        url: "https://appstore.dev.redora.com/apps/store"
    }; //voltmx.application.openURL("https://maps.google.com")
    //Theme
    Global.vars.crntTheme = "";
    //time
    Global.vars.newTimeSet = false;
    //person globals
    Global.vars.personCountryType = "";
    Global.vars.nationalitypreset = 1;
    Global.vars.checkdocument = "";
    Global.vars.indDcocumentChecked = null;
    Global.vars.indDcocumentValidated = null;
    Global.vars.documentTypeCheckable = null;
    Global.vars.documentCountryPersonManual = false;
    Global.vars.gPersonGenderResult = [];
    Global.vars.personDeleteButtons = false;
    Global.vars.continueToFormAfterDataReset = "";
    Global.vars.maxAge = 112;
    Global.vars.minAge = 12;
    Global.vars.maxHaltAge = 18;
    Global.vars.execByPartner = null;
    Global.vars.rightsRead = null; //Cautie
    Global.vars.StatementNoPledge = ""; //Cautie tekst type == 20
    Global.vars.offenceCommunicated = null;
    Global.vars.OffenceCommunicatedNoStatement = ""; //Strafbaar feit niet medegedeeld tekst type == 20
    Global.vars.officerNameStatementRecordedBy = null;
    Global.vars.officerNumberStatementRecordedBy = null;
    Global.vars.legalAssistCommunicated = null;
    Global.vars.usesLegalAssistance = null;
    Global.vars.declinedLegalAssistance = null;
    Global.vars.enableDefaultRepealText = false;
    Global.vars.interpreter = null;
    Global.vars.interpreterCommunicated = null;
    Global.vars.interpreter = null;
    Global.vars.translationLanguage = null;
    Global.vars.translationLanguageDesc = null;
    Global.vars.statementEdited = null;
    Global.vars.statementKey = null;
    Global.vars.statementText = ""; //Verklaring verdachte tekst type == 2
    Global.vars.showButtonCheckdocument = true;
    //cancel after cautie nee/rightsread no
    Global.vars.cancelAfterRightsReadFalse = true;
    Global.vars.setDateManual = true;
    Global.vars.additionalDocumentTypes = [{
        code: "overig",
        numbervalue: 9900,
        description: "Overig",
        country_number_mandatory: false,
        document_code: "O"
    }, {
        code: "overig",
        numbervalue: 9901,
        description: "Identiteitskaart buitenlands",
        country_number_mandatory: true,
        document_code: "I"
    }, {
        code: "overig",
        numbervalue: 9902,
        description: "Paspoort max 5 jaar verlopen",
        country_number_mandatory: true,
        document_code: "P5"
    }, {
        code: "overig",
        numbervalue: 9903,
        description: "Paspoort buitenlands",
        country_number_mandatory: true,
        document_code: "P"
    }, {
        code: "overig",
        numbervalue: 9904,
        description: "Rijbewijs buitenlands",
        country_number_mandatory: true,
        document_code: "RBW"
    }, {
        code: "overig",
        numbervalue: 9905,
        description: "Vreemdelingen document",
        country_number_mandatory: true,
        document_code: "IW"
    }, {
        code: "overig",
        numbervalue: 9906,
        description: "Ambtelijk vastgesteld",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9907,
        description: "Ambtshalve bekend",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9908,
        description: "Schoolpas",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9909,
        description: "Persoonlijke OV-Chipkaart (met foto)",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9910,
        description: "Patiëntenpas (met pasfoto)",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9911,
        description: "Geldig vervoersabonnement (met pasfoto)",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9912,
        description: "Andere pas (met pasfoto)",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9913,
        description: "Bankpas & ander document",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9914,
        description: "Fotoherkenning (fotonummer)",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9915,
        description: "Gewaarmerkt kopie van wettelijk document",
        country_number_mandatory: false,
        document_code: null
    }, {
        code: "overig",
        numbervalue: 9916,
        description: "iDIN",
        country_number_mandatory: false,
        document_code: null
    }];
    Global.vars.specifyLocationValues = [{
            key: furtherIndicationType.others,
            value: "Overig"
        }, {
            key: furtherIndicationType.atTheStation,
            value: "Op het station"
        }, {
            key: furtherIndicationType.inTheTrain,
            value: "In de trein"
        }, {
            key: furtherIndicationType.alongTheTrack,
            value: "Langs het spoor"
        } 
    ];
    Global.vars.Personsaddresses = {
        country: null,
      	countryCode: null,
      	street: null,
        streetNumber: null,
        streetNumAdditn: null,
        zipcode: null,
        city: null,
        cityCode: null,
        addressline1: null,
        addressline2: null,
        addressline3: null
    };
    Global.vars.findCountryOfBirth = false;
    Global.vars.originalPersonDocumentInfo = {};
    Global.vars.showPersonWarnings = true;
    //Case globals
    Global.vars.gCaseVehicles = {};
    Global.vars.gCaseVehiclesIndex = 0;
    Global.vars.gCaseVehiclelpImage = null;
    Global.vars.gCaseVehicleAnprImage = null;
    Global.vars.gCasePersons = {};
    Global.vars.gCasePersonsIndex = 0;
    Global.vars.lastTaskProcessedPrevious = {}; //CaseData.processinfo.lastTaskProcessed 
    Global.vars.serviceTaskValues = {};
    Global.vars.taskRemark = "";
    Global.vars.alreadyClaimed = false;
    Global.vars.historicCaseData = null;
    Global.vars.noViolation = null;
    //
    Global.vars.vehicleDeleteButtons = false;
    Global.vars.gSearchtype = "typegroup";
    Global.vars.vehicleCharacteristicsBack = false;
    Global.vars.allVehicleTypeGroups = [];
    Global.vars.RegionDuplicatePopupSet = false;
    //Quickstart
    Global.vars.startMode = "frmFollow";
    //Activities + Active cases
    Global.vars.activityEndTimeMinusMinutes = 5;
    Global.vars.activeCasesDaysToLookBack = 999;
    Global.vars.queryUnclaimed = {};
    Global.vars.indexToUseUnclaimedCases = "";
    Global.vars.indexToUseUnclaimedCasesByUser = "";
    Global.vars.indexToUseUnclaimedCasesCloud = "";
    Global.vars.base64MapImage = "";
    Global.vars.maxCouchDBCases = 20;
    Global.vars.activeCasesMinutesToLookBack = 7200;
    Global.vars.couchUrl = "";
    Global.vars.daysToLookBack = 90;
    // Policy
    Global.vars.favoritePolicies = []; // stored at file system
    Global.vars.gPolicySearchModus = "search";
    Global.vars.previousPolicySearchModus = "search";
    //Offence
    Global.vars.favoriteOffences = []; // stored at file system
    Global.vars.gOffenceSearchModus = "search";
    Global.vars.previousOffenceSearchModus = "search";
    Global.vars.gSearchtypeOffenceSelect = "offence";
    Global.vars.chosenThemeCode = "";
    Global.vars.selectedFocusedTheme = {};
    Global.vars.lastThemeHeader = "";
    Global.vars.themeModus = "";
    Global.vars.themeBack = false;
    Global.vars.OffenceSelectPrevious = false;
    Global.vars.offenceTypeTyping = "";
    Global.vars.gOffencePreviousId = [];
    Global.vars.gOffenceBreadCrumb = [];
    Global.vars.stationBreadCrumb = [];
    Global.vars.tasktypeBreadCrumb = [];
    Global.vars.getOutcometypes = {};
    Global.vars.selectedOutcome = null;
    Global.vars.gOffenceHeaderText = "";
    Global.vars.gOffenceTypeHeaderText = "";
    Global.vars.gOffenceSearchText = "";
    Global.vars.gOffenceCatsSearchText = "";
    Global.vars.gOffencePrevious = [];
    Global.vars.gOptionUsage = null;
    Global.vars.directOffenceSelect = false;
    Global.vars.amountextraimposition = "0.00";
    Global.vars.tariffNHAHandicapped = "1.50";
    Global.vars.tariffNHA = "0.00";
    Global.vars.minimumNumberOfPhotos = 0;
    Global.vars.maximumNumberOfPhotos = 0;
    Global.vars.validatePhotosForCaseType = [];
    Global.vars.noCautionPersonCaseType = [{
        "caseType": "MLD_PV_VG"
    }];
    Global.vars.ticketTypes = null;
    Global.vars.authorizedTicketType = false;
    Global.vars.authorizedTicketTypeInList = true;
    Global.vars.questionTypeChooseEnabled = false;
    //Active cases
    Global.vars.getMapImage = [];
    Global.vars.defaultCity = '';
    Global.vars.chosenCity = "";
    Global.vars.cancelCase = false;
    Global.vars.resetAppTrackdown = false;
    Global.vars.signalVehicleEnabled = true;
    Global.vars.getBoatInfoEnabled = false;
    Global.vars.superCaseEnabled = false;
    //FAQ NS
    Global.vars.useFaqNS = false;
    //READ ID
    Global.vars.readIDScanned = false;
    Global.vars.readIDTestSet = null;
    voltmx.print("### Resetting Global.vars.readID");
    Global.vars.readID = {
        MRZ_documentNumber: "",
      	MRZ_documentCode: "",
        MRZ_dateOfBirth: "",
        MRZ_dateOfExpiry: "",
        MRZ_response: null,
        fullname: "",
        birthdate: "",
        birthdateDesc: "",
        birthdateComponents: [],
        yearOfBirth: "",
        documentNumber: "",
        ssn: "",
        surname: "",
        middlename: "",
        givenNames: "",
        idenDocType: "",
        idenDocTypeDesc: "",
        issuingCountryCode: "",
        countryIdenDocDesc: "",
        countryIdenDoc: null,
        gender: "",
        genderDesc: "",
        indicationDutch: "",
        nationality: "",
        nationalityDesc: "",
        countryOfBirthDesc: "",
        birthplace: "",
        originalNFCOutPut: {},
        nfcVerificationStatusDS: "",
        nfcVerificationStatusHT: "",
        nfcVerificationStatusCS: "",
        nfcVerificationStatusCSReason: "",
        nfcVerificationStatusAA: "",
        nfcVerificationStatusEACCA: "",
        indMRZ: false, //boolean
        indNFC: false, //boolean
        indManual: false //boolean
    };
    Global.vars.useDemo = false;
    Global.vars.documentTypes = [];
    Global.vars.checkedDutch = "done";
    //
    Global.vars.printOffenceTypeDesc = "";
    Global.vars.printOffenceTypeFooter = "";
    Global.vars.printIdentTypeDesc = "";
    Global.vars.printObservationOfficer = "";
    Global.vars.printInterrogationSuspect = "";
    Global.vars.printStoryFinding = "";
    Global.vars.printStatementPledge = "";
    Global.vars.printStatementNoPledge = "";
    Global.vars.printTicketTemplate = "";
    // != NL0004
    Global.vars.dismissNotification = false;
    Global.vars.germanCarFormatted = false;
    Global.vars.carChecked = false;
    Global.vars.validatePersonDocument = true;
    // RWS NL0003
    Global.vars.anprScanDisabled = false;
    Global.vars.locationWithinUrbanArea = true; //default true false for RWS/NL0003
    // BE0001
    Global.vars.enableManualAdress = true;
    Global.vars.enableErrorSound = false;
    Global.vars.enableErrorVibrate = false;
    Global.vars.sendLicensePlateImage = false;
    Global.vars.setDefaultVehicleTypeGroup = false;
    Global.vars.useOffenceShortDescription = false;
    Global.vars.regionCodeEnabled = true;
    Global.vars.regionCodeEditable = false;
    Global.vars.disableRecheck = false;
    Global.vars.followBtnRecheckEnabled = false;
    Global.vars.handleEditHousenumberEnabled = false;
    Global.vars.editAfterCheckCasefrmTrackDown = false;
    Global.vars.germanSeparatorMandatory = true;
    Global.vars.dynamicMarkerColorFollow = false;
    // Rotterdam RL0005 NL0036 
    Global.vars.indTowedEnabled = false;
    Global.vars.disableSwipeMenuSettings = false;
    //ObjectSync Globals
    Global.vars.objectSyncOptions = {};
    //  	Global.vars.ObjServiceObject = null;
    Global.vars.ObjServiceThemeObject = null;
    Global.vars.ObjServiceAppVersionObject = null;
    //   	Global.vars.syncObjects = null;
    //   	Global.vars.ObjServiceVehicleObject = null;
    //   	Global.vars.areasObj = null;
    Global.vars.runningLogonProcess = false;
    Global.vars.objectSyncSetupDone = false;
    Global.vars.objectSyncAppVersionSetupDone = false;
    Global.vars.appInstanceRecord = null;
    // NL0027
    Global.vars.disablePlaySound = false;
    Global.vars.disableShowCircle = false;
    //	
    Global.vars.checkLicensePlateInput = true;
    Global.vars.countryPlateUnknownAllowed = false;
    Global.vars.manualInputLegalEntityEnabled = false;
    // ticketType
    Global.vars.enforcementLevel = "F"; // "F"  Fine "W" Warning
    //Exception handler
    Global.vars.ExceptionMessage = "";
    //location unlocked
    Global.vars.locationJustUnlocked = false;
    //video resolution
    Global.vars.bestVideoResolution = null;
    //Parking card check
    Global.vars.checkParkingCard = [];
    Global.vars.cardCheckonCheckVehicle = true;
    //ANPR API - ARH base64
    Global.vars.ANPRapiRunning = false;
    //Follow
    Global.vars.gSchedules = [];
    //ns popup bij statement
    Global.vars.popupStatementLanguages = {};
    //parking zones polygons
    Global.vars.parkingZonesMap = [];
    voltmx.print("### Global_initGlobals clear selectedAreas");
    Global.vars.selectedAreas = null;
    Global.vars.areasLoaded = false;
    Global.vars.preserve = [];
  	Global.vars.preserveData = {};
    //show button Done/Next on frmLocation
    Global.vars.changeLocationDoneButton = false;
    //frmTrackDown back from ANPR photo
    Global.vars.backFromANPRPhoto = false;
    Global.vars.continuesANPR = false; //instanceparameter
    Global.vars.assessANPRPhoto = false;
    Global.vars.iOSCameraOpened = false;
    //handhaaf object laten zien
    Global.vars.useEnforcementObjectService = false;
    Global.vars.enforcementObjectServiceStartPage = 0;
    Global.vars.enforcementObjectSearch = false;
    Global.vars.gFocusedCharacteristic = null;
    Global.vars.gCaseEnforcementObject = null;
    Global.vars.questionTypes = [];
    Global.vars.questionTypesUsage = "";
    Global.vars.questions = [];
    Global.vars.selectedQuestionTypes = [];
    Global.vars.selectedQuestionType = null;
    Global.vars.selectedQuestionIndex = null;
    Global.vars.QuestionsSet = false;
    Global.vars.questionsClearedOnResume = false;
    Global.vars.defTaskOutcomeFrmResumeHoreca = "afgehandeld_horeca";
    Global.vars.defTaskOutcomeFrmResumeBevinding = "afgehandeld_bevinding";
    Global.vars.defTaskOutcomeFrmResumeTikVerbaal = "afgehandeld_tikverbaal";
    Global.vars.transportTicketFineAmount = 50; //instanceparameter
    Global.vars.transportTicketBusRideAmount = 4; //instanceparameter
    Global.vars.UVB = {
        "UVB": true,
        "UVBOffenceCodes": ["E100A", "E103"],
        "description": "UVB",
        "bikesCost": 6.9
    }; //instanceparameter
    Global.vars.governance = {
        "governance": true,
        "governanceOffenceCodes": ["H022"],
        "description": "Bestuursdwang"
    }; //instanceparameter
    Global.vars.busOrTrainstationSelect = null;
    Global.vars.busOrTrainStations = [];
    Global.vars.busOrTrainStationBreadCrumb = [];
    Global.vars.halteOrder = [];
    Global.vars.removeCaseData = null; //JSON.parse(JSON.stringify(CaseData));
    Global.vars.copyCaseData = {
        "person": [],
        "vehicle": [],
        "time": {},
        "location": {},
        "setpastebutton": false
    };
    Global.vars.showProhibitions = false; //instantieparameter
    Global.vars.defTaskOutcomeProhibitions = "afgehandeld_verbod"; //instantieparameter
    Global.vars.defTaskOutcomeCorrectCandidate = "verbodOverhandigd";
    Global.vars.defTaskOutcomeSendProhibition = "verbodVerstuurd";
    Global.vars.caseTypeCategoryDescription = "";
    Global.vars.prohibitionsStationSearch = false;
    Global.vars.trainRideAmount = 0;
    Global.vars.PDFProhibitionsName = "";
    Global.vars.prohibitionEmailDomain = [{
        "domain": "parkius.io"
    }, {
        "domain": "redora.nl"
    }];
    Global.vars.currentPersonInformationProhibitions = [];
    Global.vars.stationSearchProhibitions = "";
    Global.vars.stationCodeSearchProhibitions = "";
    Global.vars.saveLocationForProhibition = null;
    Global.vars.prohibitionsFillPoliceOfficerEmailFromCase = true;
    Global.vars.prohibitionsFillPoliceOfficerMobileFromCase = true;
    Global.vars.vehicleBrandOrder = [];
    //Amount Bestuurlijke boete
    Global.vars.governmentalEnforcementAmount = 50;
    Global.vars.maxoccurences = 0;
    Global.vars.originalOffenceAmounts = {
        "amount": null,
        "amountDisplay": null,
        "amountExtra": null,
        "amountExtraDisplay": null
    };
    Global.vars.maxWarningsReached = false;
    //Follow globals
    Global.vars.gLatitudeLast = 0.0;
    Global.vars.gLongitudeLast = 0.0;
    //kroondomein
    Global.vars.cardCheckBarcodePrefixNL0022 = "466940000";
    //
    Global.vars.afterLogOnContinue = false;
    //offline concepts
    Global.vars.OfflineConceptscaseToSend = null;
    //version
    Global.vars.continueAppVersionOK = true;
    //network
    Global.vars.noNetworkCallRaised = false;
    //sharedBikes
    Global.vars.kindOfSharedBikes = [{
        value: "Felyx",
        description: "Felyx"
    }, {
        value: "GoSharing",
        description: "GoSharing"
    }, {
        value: "Donkey Republic",
        description: "Donkey Republic"
    }, {
        value: "Check",
        description: "Check"
    }]; //instantieparam
    Global.vars.registerLabelCaseLoaded = false;
    Global.vars.labelRegisteredLocation = null;
    Global.vars.labelRegisteredTime = null;
    Global.vars.checkLabelRadius = 100;
    Global.vars.labelSharedBikes = false;
    Global.vars.labelCasesBookmark = null;
    Global.vars.labelOutcomeOtherLocation = "AndereLocatie";
    Global.vars.labelOutcomeTakeExtraPicture = "OpDeWagen";
    Global.vars.currentLabelPhotos = 0;
    Global.vars.fromOverViewLabel = false;
    Global.vars.cameToVehicleFromForm = "";
    Global.vars.cameToLocationFromForm = "";
    Global.vars.cameToPersonFromForm = "";
    Global.vars.cameToEnforcementObjectFromForm = "";
    Global.vars.cameToTrainRideFromForm = "";
    Global.vars.cameToBusRideFromForm = "";
    Global.vars.cameToOnBusStationFromForm = "";
    Global.vars.cameToTrainTrackFromForm = "";
    Global.vars.cameToOnStationFromForm = "";
    Global.vars.bulkOutcomes = [{
        "identification": "NietAangetrVerwijderen"
    }, {
        "identification": "NietAangetroffenSchouwen"
    }];
    Global.vars.generalAlertRaised = false;
    Global.vars.labelAreaCaseTypes = [{
        "caseType": "WEF"
    }, {
        "caseType": "VGF"
    }];
    Global.vars.relabelOutcome = "Herlabelen";
    //Global.vars.labelCheckDistanceTask = "WachtenBegunstigingstermijn";
    Global.vars.labelCheckDistanceTask = [{
        "task": "WachtenBegunstigingstermijn"
    }, {
        "task": "Schouwen"
    }, {
        "task": "FietsVerwijderen"
    }];
    Global.vars.nonPhotoRemovalTasks = [{
        "task": "WachtenBegunstigingstermijn"
    }, {
        "task": "Schouwen"
    }, {
        "task": "FietsVerwijderen"
    }, {
        "task": "Transporteren"
    }];
    Global.vars.labelVehicleTypes = [{
        "organisation": "ITN",
        "vehicleTypeGroupCode": "RW",
        "IDENT_TYPE_VTE": "00",
        "VehicleTypes": [{
            "code": "FS"
        }]
    }, {
        "organisation": "RDW",
        "vehicleTypeGroupCode": "C",
        "IDENT_TYPE_VTE": vehicleIdentType.vehicle,
        "VehicleTypes": [{
            "code": "BB"
        }, {
            "code": "BD"
        }, {
            "code": "SP"
        }, {
            "code": "BF"
        }, {
            "code": "BS"
        }, {
            "code": "FE"
        }]
    }];
    Global.vars.checkLabelColorBrand = {
        "outcomes": [{
            "identification": "OpDeWagen"
        }],
        "caseTypes": [{
            "caseType": "WEF"
        }, {
            "caseType": "FWR"
        }]
    };
    Global.vars.haveToLabelTask = "LabelenFietsenCS";
    Global.vars.notToRegisterCaseTypes = [{
        "caseType": "CSF"
    }];
    Global.vars.extendedReportCaseTypeCategories = [{
        "caseTypeCategory": "hav"
    },{
        "caseTypeCategory": "hav"
    },{
        "caseTypeCategory": "hav"
    },{
        "caseTypeCategory": "hav"
    }];
    Global.vars.labelFilterTasks = [{
        "taskType": "Schouwen",
        "taskTypeDescription": "Schouwen"
    }, {
        "taskType": "FietsVerwijderen",
        "taskTypeDescription": "Verwijderen"
    }, {
        "taskType": "LabelenFietsenCS",
        "taskTypeDescription": "Labelen fietsen CS"
    }]; //moet misschien nog instantieparam worden
    Global.vars.caseTypeLabelColor = [{
        "caseType": "EVE",
        "labelColor": "yellow"
    }, {
        "caseType": "FWR",
        "labelColor": "red"
    }, {
        "caseType": "WEF",
        "labelColor": "blue"
    }, {
        "caseType": "GVV",
        "labelColor": "purple"
    }, {
        "caseType": "WZH",
        "labelColor": "orange"
    }, {
        "caseType": "CSF",
        "labelColor": "green"
    }, {
        "caseType": "HGF",
        "labelColor": "black"
    }, {
        "caseType": "VGF",
        "labelColor": "pink"
    }, {
        "caseType": "WAA",
        "labelColor": "lightblue"
    }];
    Global.vars.cancelPropositionInterval = 0;
    Global.vars.queueLengthTimer = 0;
    Global.vars.queueLengthLimit = 50;
    Global.vars.queueLengthMaxLimit = 500;
    Global.vars.useQueueLengthTimer = "no";
  	Global.vars.scanUnitQueueLength = [{
            key: "none",
            value: ""
        }];
    Global.vars.queueInUse = false;
    Global.vars.useCognitoLogin = false;
    Global.vars.cognitoLoginDomains = []; //[{"domain":"redora.nl"}, {"domain":"rotterdam.nl"}];
    Global.vars.useMobileAppAzureAD = true;
    Global.vars.mobileAppAzureADDomains = [{
        "domain": "redora.nl"
    }, {
        "domain": "twyns.com"
    }]; //, {"domain":"rotterdam.nl"}];
    Global.vars.retrieveCasesTimer = 300; //5 min
    Global.vars.photoMetadataVisible = true; //5 min
    Global.vars.manualStreetInputEnabled = false;
    Global.vars.waterWayMarkerRadius = 1000;
    Global.vars.indUseWaterWayMarkers = false;
    // nwe Registreren
    Global.vars.themeSelection = [];
    Global.vars.themeCode = null;
    Global.vars.themeCodeDesc = "";
    Global.vars.themeCategory = null;
    Global.vars.masterThemeCode = null;
    Global.vars.masterThemeCodeDesc = "";
    Global.vars.themeCaseType = null;
    Global.vars.themeCodeHasDetailTheme = false;
    Global.vars.themeCodeHasOffence = false;
    Global.vars.VteCode = null;
    Global.vars.project1Key = null;
    Global.vars.project1 = null;
    Global.vars.project2Key = null;
    Global.vars.project2 = null;
    Global.vars.projects = []; //vullen instantieparam voorlopig
    Global.vars.uniformCivilian = null;
    Global.vars.bvhCodes = []; //vullen instantieparam voorlopig
    Global.vars.bvhCode = null;
    Global.vars.masCodes = []; //vullen instantieparam voorlopig
    Global.vars.masCode = null;
    Global.vars.registerConceptEnabled = false;
    Global.vars.ageCategory = null;
    Global.vars.alwaysUseDefaultCountryCode = false;
    Global.vars.openTasksToQuery = []; //moet een instantieparam worden - testvulling staat nu in SyncUtil bovenaan
    Global.vars.overviewTasks= [];
    Global.vars.casesSelectedIndices = null;
  	Global.vars.tasksSelectedIndices = null;
  	Global.vars.overViewTaskListview = false;
  	Global.vars.taskTypesToQueryInitiatorOnly = '';
    //bike labels within radius
    Global.vars.maxSliderValueOverviewLabel = 400; //in meters
    Global.vars.maxSliderValueOverviewTask = 500; //in meters
    Global.vars.locationWithinRadius = 100; //in meters
    // validate Case on RegisterResume
    Global.vars.missingOffenceData = false;
    Global.vars.signatureSet = false;
    Global.vars.defaultOutcomeRemoveConcept = "P98.07.T02.O01"; // Concept Aangevuld
    Global.vars.defaultOutcomeChangeConcept = "P98.07.T02.O03"; // Concept Aangevuld
    Global.vars.authorisedCaseTypes = [{
        "caseType": "CONTROL_L"
    }, {
        "caseType": "CONTROL_F"
    }];
    Global.vars.authorisedTaskTypes = [{
        "taskType": "OpvolgenControleOpStraat"
    }];
  	openTaskTypes = [{
        "taskType": "EditTicket",
    }, {
       	"taskType": "AanvullenWaarschuwing"
    }, {
       	"taskType": "NL0043.P04.01.T05"
    }];
  	Global.vars.openTaskTypesToQuery = '""';
  	Global.vars.unhandledTaskTypes = [{
        "taskType": "NL0042.P01.02.T03"
    },{
        "taskType": "NL0042.P01.03.T01"
    }];
  	Global.vars.verifyAddressTaskType = [{
        "taskType": "NL0042.P01.02.T03"
    }];
  	Global.vars.mandatoryForOutcome = [{
        "outcome": "NL0042.P01.02.T03.O02"
    },{
        "outcome": "NL0042.P01.03.T01.O01"
    },{
        "outcome": "NL0042.P01.03.T01.O04"
    }];
  	Global.vars.notMandatoryForOutcome = [{
        "outcome": "RL0003.P99.05.T03.O01"
    }];
  	Global.vars.resumeDisabledForOutcome = [{
        "outcome": "NL0043.P04.01.T05.O01"
    }];
  	Global.vars.showRadiusFilterOnly = false; // true for NL0042
    Global.vars.resetUserViaLogOff = false; // true for NL004
  	Global.vars.markertakenImage = "";
    // NL0003
    Global.vars.skipHectometerObjectSync = false;
    //Global.vars.skipHectometerObjectSync = true;
    // Rotterdam - BRR
    Global.vars.checkLocationObject = {};
    // OverviewLabel
    Global.vars.casesOverviewLabel = [];
    // OverviewLTask
    Global.vars.casesOverviewTask = [];
    //showKindOfRoad
    Global.vars.showKindOfRoad = true;
    //
    Global.vars.retryRetrieveCase = 0;
    // indiation that user tried to lgin is not allowed to login
    Global.vars.cameBackFromOffenceSelect = false;
    //
    Global.vars.inCorrectUserPopupShowed = false;
    //
    Global.vars.municipalities = [];
    Global.vars.cities = [];
    //
    Global.vars.onClickSignatureCancelButton = false;
    Global.vars.removeRDWInfoFromParkingServiceResult = false;
    // purpose BE0001 do not checkCase after manual location change
    Global.vars.disableCheckCaseOnEdit = false;
    Global.vars.doCheckCaseOnEdit = true;
    Global.vars.addAddressWithoutGPS = true;
    Global.vars.indCommentrequired = false;
    Global.vars.isNumberCheckOnAddressline2 = true;
    // overviewTasks
    Global.vars.scanUnitSelected = "all";
  	Global.vars.authorizedScanUnits = [{"brandType":"ScanGenius"},{"brandType":"SCANaCAR"}];
  	Global.vars.scanUnits = [{id : "sgslim016", description : "Arvoo 1"},
             {id : "sgslim017", description : "Arvoo 2"},
             {id : "sgslim018", description : "Arvoo 3"},
             {id : "sgslim019", description : "Arvoo 4"}];

    Global.vars.clampRequestStatus = "NL0042.P01.02.S10"; //NL0042
    Global.vars.unClampRequestStatus = "NL0042.P01.03.S05"; //NL0042
  	Global.vars.officerOnLocationStatus = "GebruikerOpLocatie";
    Global.vars.externalDocumentScan = false;
    // resume pre application init (BEWARE: PLACE ALL VARIABLES BEFORE THIS CALL !!!!)
    Global_preApplicationInitAfterInitGlobals();
  	Global.vars.appState = "";
}

function Global_preApplicationInitAfterInitGlobals() {
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false) {
        Global.pdfObject = new REDLINE.PDF();
    } else {}
}

function preApplicationInit(){
  voltmx.print("#### preApplicationInit");
  voltmx.license.disableMetricReporting();
  voltmx.logger.currentLogLevel = voltmx.logger.logLevel.ERROR;
  //#ifdef android
  voltmx.lang.setUncaughtExceptionHandler(Global_handleException);
  //#else
  voltmx.lang.setUncaughtExceptionHandler(Global_handleException);
  //#endif
  // init Global variables
  Global_initGlobals();
  // get device info
  Global_getDeviceInfo();
}

function Global_handleException(exceptionObject) {
    voltmx.print("### Global_handleException exception has occured");
    voltmx.print("### Global_handleException exceptionObject: " + JSON.stringify(exceptionObject));
    voltmx.application.dismissLoadingScreen();
    // Converting exception object into a readable string
    var exceptionString = "";
    if ("sourceURL" in exceptionObject) {
        exceptionString += exceptionObject.sourceURL;
    }
    if ("line" in exceptionObject) {
        exceptionString += " line " + exceptionObject.line;
    }
    if ("stack" in exceptionObject) {
        voltmx.print("### Global_handleException stack: " + JSON.stringify(exceptionObject.stack));
        exceptionString += " StackTrace: " + exceptionObject.stack;
    } else if ("message" in exceptionObject) {
        if (voltmx.application.getCurrentForm() != null) {
            exceptionString += " : " + exceptionObject.message + " , in form: " + voltmx.application.getCurrentForm().id;
        }
    }
    //Logging the exception string to console
    if (voltmx.application.getCurrentForm() != null) {
        voltmx.print("### Global_handleException Unhandled Exception:" + exceptionString + " , in form: " + voltmx.application.getCurrentForm().id);
    }
    Global.vars.ExceptionMessage = exceptionString + " on Environment: " + Global.vars.environment;
    Global_recordException();
}

function Global_showExceptionAlert() {
    var popup = new popups.exception({
        "clipBounds": true,
        "id": "exception",
        "isVisible": true,
        "zIndex": 31,
        "top": "0dp",
        "left": "0dp",
        "layoutType": voltmx.flex.FREE_FORM,
        "masterType": constants.MASTER_TYPE_DEFAULT,
        "width": "100%"
    }, {}, {});
    voltmx.application.getCurrentForm().add(popup);
}

function Global_recordException() {
    voltmx.print("### Global_recordException");
    voltmx.application.openURL("https://sharedweb.prd.eu-central-1.rl.twyns.com");
    try {
      voltmx.timer.schedule("CloseApp", Global_exitAPP, 2, false);
    } catch (err) {}
}

function Global_setExceptionCloseApp() {
    Global_recordException();
}

function postApplicationInit() {
    voltmx.print("#### postApplicationInit ####");
  	// get Languagecode
    Utility_getCurrentLocale();
    // set menu
    Global_setMenuSettings();
    //#ifdef android
    if (voltmx.theme.getCurrentTheme() == "REDORA") {
      //Redora
      voltmx.application.setApplicationProperties({
        statusBarColor: "184a83"
      }); //Blue
    } else if (voltmx.theme.getCurrentTheme() == "Egis") {
      //Egis
      voltmx.application.setApplicationProperties({
        statusBarColor: "9ec536"
      }); //Green 
    } else if (voltmx.theme.getCurrentTheme() == "Arvoo") {
      //Arvoo
      voltmx.application.setApplicationProperties({
        statusBarColor: "f57f22"
      }); //Orange
    } else if (voltmx.theme.getCurrentTheme() == "REDORAopl") {
      //RedoraOpl
      voltmx.application.setApplicationProperties({
        statusBarColor: "c20015"
      }); //Green 
    }
    //video resolution
    Global.vars.bestVideoResolution = Utility_getAndroidCameraResolutions();
    voltmx.print("### postApplicationInit Global.vars.bestVideoResolution: " + JSON.stringify(Global.vars.bestVideoResolution));
    //#endif
    // get printer
    Global.vars.gPrinter = voltmx.store.getItem("printer");
    voltmx.print("### postApplicationInit: " + JSON.stringify(Global.vars.gPrinter));
    if (Global.vars.gPrinter === null) {
        Global.vars.gPrinter = {
            "MacAddress": "",
            "Name": ""
        };
        voltmx.store.setItem("printer", Global.vars.gPrinter);
    } else if (typeof Global.vars.gPrinter.MacAddress == "undefined") {
        voltmx.print("### REINIT Global.vars.gPrinter ###");
        Global.vars.gPrinter = {
            "MacAddress": "",
            "Name": ""
        };
        voltmx.store.setItem("printer", Global.vars.gPrinter);
    } else {
        voltmx.print("### postApplicationInit: Print_initModule ");
        Print_initModule();
    }
    Utility_checkAppModus();
    //get services
    Global_setServerInfo();
}

function Global_savePrinter(name, macaddress) {
    Global.vars.gPrinter.MacAddress = macaddress;
    Global.vars.gPrinter.Name = name;
    voltmx.store.setItem("printer", Global.vars.gPrinter);
}

function Global_getEncryptionParameter(input) {
    for (var i in Global.vars.encryptionParams) {
        var v = Global.vars.encryptionParams[i];
        if (v.environment === Global.vars.environment) {
            if (input === "vector") {
                return Utility_decompress(v.vector);
            } else if (input === "passphrase") {
                return Utility_decompress(v.passphrase);
            }
        }
    }
}

function Global_setServerInfo() {
    Global_getCorrectEndPoint();
    if (voltmx.net.isNetworkAvailable(constants.NETWORK_TYPE_ANY)) {
        voltmx.print("#### Global_setServerInfo endpoints: " + JSON.stringify(Global.vars.endpoints));
        konySDK = new voltmx.sdk();
        //find endpoints etc
        for (var i in Global.vars.endpoints) {
            var v = Global.vars.endpoints[i];
            if (v.environment == Global.vars.environment) {
                voltmx.print("#### Global_setServerInfo init v: " + JSON.stringify(v));
                konySDK.init(Utility_decompress(v.appKey), Utility_decompress(v.appSecret), Utility_decompress(v.endpoint), Global_setServerInfoSuccesCallback, Global_setServerInfoErrorCallback);
                Global.vars.couchUrl = v.couchUrl;
                if (v.name !== undefined) {
                    Global.vars.environmentName = v.name;
                } else {
                    Global.vars.environmentName = "";
                }
                break;
            }
        }
        voltmx.print("#### Global_setServerInfo Global.vars.environmentName: " + Global.vars.environmentName);
    } else {
        voltmx.application.dismissLoadingScreen();
        Global.vars.setServerInfoDone = false;
    }
}

function Global_setAdalClientID() {
    if (Global.vars.environment == "development" || Global.vars.environment == "test") {
        //redora
        //voltmx.print("### Global_setAdalClientID redora client id");
        Global.vars.adalClientID = "eecf9eb7-ca5d-4ba2-90bf-67517aef37d8";
    } else if (Global.vars.environment == "acceptance" || Global.vars.environment == "production") {
        //nslogin
        //voltmx.print("### Global_setAdalClientID ns client id");
        Global.vars.adalClientID = "35468cb1-f088-4451-91cc-0f008c3d45b7";
    }
    //voltmx.print("### Global_setAdalClientID: " + Global.vars.adalClientID);
}

function Global_setServerInfoSuccesCallback(result) {
    voltmx.application.dismissLoadingScreen();
    voltmx.print("### Global_setServerInfoSuccesCallback: " + JSON.stringify(result));
    voltmx.print("### Global_setServerInfoSuccesCallback current form: " + voltmx.application.getCurrentForm().id);
    Global.vars.setServerInfoDone = true;
    Global.vars.availableIDP = [];
  	if (result != null && result.config != null && result.config.login != null && result.config.login.length > 0){
      //voltmx.print("### Global_setServerInfoSuccesCallback result.config.login : " + JSON.stringify(result.config.login ));
      var login = result.config.login;
      login.forEach((item) => {
        //voltmx.print("### Global_setServerInfoSuccesCallback identity : " + item.prov);
        //voltmx.print("### Global_setServerInfoSuccesCallback type : " + item.type);
        if (item.prov != null && item.type != null && item.type !== 'anonymous'){
          var record = {
            identity : item.prov,
            domain : (item.prov === Global.vars.selectedOAuthServiceProvider ? "" : item.prov.replace("idp", ""))
          } ;
          Global.vars.availableIDP.push(record);
        }
      });
      //voltmx.print("### Global_setServerInfoSuccesCallback Global.vars.availableIDP : " + JSON.stringify(Global.vars.availableIDP));
    }
  	var isLoginPersisted = false;
  	var _domain = "";
  	if(Global.vars.gEntraUsername != null && Global.vars.gEntraUsername !== ""){
      _domain = Utility_getDomainFromEmail(Global.vars.gEntraUsername);
  	} else if(Global.vars.gUsername != null && Global.vars.gUsername !== ""){
      _domain = Utility_getDomainFromEmail(Global.vars.gUsername);
  	}
    if (_domain !== ""){
      Global.vars.gIdentityProvider = Utility_validateDomain(_domain);
      Utility_storeSetItem("identityprovider", Global.vars.gIdentityProvider);
    }
    try {
      var client = voltmx.sdk.getCurrentInstance();
      var auth_client = client.getIdentityService(Global.vars.gIdentityProvider);
      isLoginPersisted = auth_client.usePersistedLogin();
      voltmx.print("### Global_setServerInfoSuccesCallback isLoginPersisted : " + isLoginPersisted);
    } catch (err){
      isLoginPersisted = false;
      voltmx.print("### Global_setServerInfoSuccesCallback isLoginPersisted error : " + JSON.stringify(err));
    }
    voltmx.print("### Global_setServerInfoSuccesCallback isLoginPersisted : " + isLoginPersisted);
    if (isLoginPersisted === true)  {
      //setup object sync
      voltmx.print("### Global_setServerInfoSuccesCallback objectSync_setup");
      objectSync_setup();
      //end setup object sync
    }
  	voltmx.print("### Global_setServerInfoSuccesCallback CurrentFormId : " + voltmx.application.getCurrentForm().id);
    if(voltmx.application.getCurrentForm().id == "frmLogin"){
        if(Global.vars.microsoftAccessToken != null){
          voltmx.application.showLoadingScreen(lblLoader,
			                                    voltmx.i18n.getLocalizedString("l_authenticating_user"),
			                                    "center",
			                                    true,
			                                    true,
			                                    { enablemenukey : true, enablebackkey : true } );
          voltmx.print("### Global_setServerInfoSuccesCallback Global.vars.gInstanceId: " + Global.vars.gInstanceId);
          if (Global.vars.gInstanceId != null){
        	voltmx.print("### Global_setServerInfoSuccesCallback service_LogonTokenInstance");
          	service_LogonTokenInstance(Global.vars.microsoftAccessToken, Global.vars.gInstanceId, frmLogin_credentialsUserCallback);
          } else {
        	voltmx.print("### Global_setServerInfoSuccesCallback service_LogonToken");
          	service_LogonToken(Global.vars.microsoftAccessToken, frmLogin_credentialsUserCallback);
          }
        } else {
          voltmx.print("### Global_setServerInfoSuccesCallback Global.vars.loggedOut: " + Global.vars.loggedOut); 
          voltmx.print("### Global_setServerInfoSuccesCallback Global.vars.firstLogin: " + Global.vars.firstLogin); 
          voltmx.print("### Global_setServerInfoSuccesCallback Global.vars.multiUserDevice: " + Global.vars.multiUserDevice); 
          voltmx.print("### Global_setServerInfoSuccesCallback Global.vars.gUsername: " + Global.vars.gUsername); 
          voltmx.print("### Global_setServerInfoSuccesCallback Global.vars.gEntraUsername: " + Global.vars.gEntraUsername); 
          if(Global.vars.gEntraUsername != null && Global.vars.multiUserDevice == "No" && Global.vars.firstLogin == "no" && Global.vars.loggedOut == "no"){  
            frmLogin_AutoLogin();
          } else if (Global.vars.setServerInfoCallBack != null){
            var callback = Global.vars.setServerInfoCallBack;
            callback();
          }
        }
    }
}

function Global_setServerInfoErrorCallback(error){
	voltmx.print("### Global_setServerInfoErrorCallback: " + JSON.stringify(error));
  	voltmx.application.dismissLoadingScreen();
  	Global.vars.setServerInfoDone = false;
    voltmx.ui.Alert( voltmx.i18n.getLocalizedString("l_retryServerconnection"),
					               Global_setServerInfoErrorCallback_alertConfirmation,
					               "confirmation",
					               voltmx.i18n.getLocalizedString("bt_yes"),
					               voltmx.i18n.getLocalizedString("bt_no"),
					               "Info",
					               null
					              );   
}

function Global_setServerInfoErrorCallback_alertConfirmation(response){
  if(response){
    Global_setServerInfo();
  }else{
    Global_exitApplication();
  }
}

function Global_setMenuSettings(){
  	Global.vars.menusettingdata = [
      	  //{ImgRight : "rightwhite.png", lblMenuSettingsItem : "Teamselect"},
      	 // {ImgRight : "rightwhite.png", lblMenuSettingsItem : "CouchDB"},
	      {ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_synchronization")},
	      //{ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_register")},
//	      {ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_selectPrinter")},
	      {ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_about")},
//     	  {ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_preferences")},
//      	  {ImgRight : "rightwhite.png", lblMenuSettingsItem : "Mulder"},
//       	  {ImgRight : "rightwhite.png", lblMenuSettingsItem : "Fiscaal"},
//       	  {ImgRight : "rightwhite.png", lblMenuSettingsItem : "Klemmen"},
//       	  {ImgRight : "rightwhite.png", lblMenuSettingsItem : "Ontklemmen"},
      	  //{ImgRight : "rightwhite.png", lblMenuSettingsItem : "Slepen"},
      	  //{ImgRight : "rightwhite.png", lblMenuSettingsItem : "PDF"},
//      	  {ImgRight : "rightwhite.png", lblMenuSettingsItem : "Read ID"},
	    ];
  	if(Global.vars.usePin == "yes"){
      Global.vars.menusettingdata.push({ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_resetPin")});
    }else if(Global.vars.usePin == "no" && Global.vars.multiUserDevice == "No"){
      Global.vars.menusettingdata.push({ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_createPin")});
    }
  	if(Global.vars.useRouting === true){
      Global.vars.menusettingdata.push({ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_preferences")});
    }
  	//if((Global.vars.usePrinter === true && Global.vars.disablePrintButtons === false) && voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false){
    if((Global.vars.usePrinter === true && Global.vars.disablePrintButtons === false)){
      Global.vars.menusettingdata.push({ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_selectPrinter")});
    }
  	if(Global.vars.useFaqNS === true){
      Global.vars.menusettingdata.push({ImgRight : "rightwhite.png", lblMenuSettingsItem : "FAQ"});
    }
//   	if(Global.vars.useDemo === true){
//       Global.vars.menusettingdata.push({ImgRight : "rightwhite.png", lblMenuSettingsItem : "Read ID"});
//     }
  	if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === false){
  		Global.vars.menusettingdata.push({ImgRight : "rightwhite.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_exit")});
      Utility_requestAndroidBLEPermissions();
    }
  	//redora
//   	Global.vars.menusettingdata = [
//       	  {ImgRight : "right.png", lblMenuSettingsItem : "Teamselect"},
// 	      {ImgRight : "right.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_synchronize")},
// 	      {ImgRight : "right.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_register")},
// 	      {ImgRight : "right.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_selectPrinter")},
// 	      {ImgRight : "right.png", lblMenuSettingsItem : voltmx.i18n.getLocalizedString("l_about")}
// 	    ];
}

function Global_GPS_callback(){
	voltmx.print("### Global_GPS_callback - fix: " + Global.vars.gGpsFix);
}

function Global_GPS_receiveMessage(event){
	voltmx.print("### Global_GPS_receiveMessage: " + JSON.stringify(event.data["message"]));
}

function Global_getDeviceInfo(){
	if ((typeof Global.vars.gDeviceInfo == "undefined")) {
	  Global.vars.gDeviceInfo = voltmx.os.deviceInfo();
      //voltmx.print("### Global.vars.gDeviceInfo: " + JSON.stringify(Global.vars.gDeviceInfo));
      for (var key in Global.vars.gDeviceInfo) {
          if (Global.vars.gDeviceInfo.hasOwnProperty(key)) {
              voltmx.print("### Global.vars.gDeviceInfo " + key + ":" + Global.vars.gDeviceInfo[key]);
          }
      }
	}
	//voltmx.print("#### Global.vars.gDeviceInfo - deviceid: " + Global_getDeviceID());
	return Global.vars.gDeviceInfo;
}

function Global_getDeviceID(){
	if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")){
		return voltmx.os.deviceInfo().identifierForVendor;
	} else {
		return Global.vars.gDeviceInfo.deviceid;
	}
}

function Global_LoadStoredValues(){
    // this is the start of using voltmx.keychain
    Utility_transform_encrypted_keys();
    // load stored values
	//voltmx.print("#### Loading the stored values");
	Global.vars.gUsername = Utility_storeGetItem("username");
	Global.vars.gEntraUsername = Utility_storeGetItem("entrausername");
	Global.vars.pinHash = Utility_storeGetItem("pinHash");
    voltmx.print("### Global_LoadStoredValues pinHash : <" + Global.vars.pinHash + ">");
  	voltmx.print("### Global_LoadStoredValues gUsername : <" + Global.vars.gUsername + ">");
  	voltmx.print("### Global_LoadStoredValues gEntraUsername 1: <" + Global.vars.gEntraUsername + ">");
  	if(Global.vars.gEntraUsername == null || Global.vars.gEntraUsername === ""){
      Global.vars.gEntraUsername = Utility_storeGetItem("username");
    }
    voltmx.print("### Global_LoadStoredValues gEntraUsername 2: <" + Global.vars.gEntraUsername + ">");
  	Global.vars.gFullName = Utility_storeGetItem("fullname");
      //Global.vars.gPassword = Utility_storeGetItem("password");
	Global.vars.gIdentityProvider = Utility_storeGetItem("identityprovider");
  	if (Global.vars.gIdentityProvider == null || Global.vars.gIdentityProvider === ""){
      var _domain = "";
      if(Global.vars.gEntraUsername !== ""){
        _domain = Utility_getDomainFromEmail(Global.vars.gEntraUsername);
      }
      Global.vars.gIdentityProvider = Utility_validateDomain(_domain);
    }
	Global.vars.gInstanceId = voltmx.store.getItem("instance");
	//voltmx.print("#### Loaded instanceId: " + Global.vars.gInstanceId);
  	Global.vars.environment = voltmx.store.getItem("gEnvironment");
  	if (Global.vars.environment == null) {
        voltmx.print("### Global_LoadStoredValues 1 Global.vars.environment: " + Global.vars.environment);
    	Global.vars.environment = "production";
    }
    voltmx.print("### Global_LoadStoredValues 2 Global.vars.environment: " + Global.vars.environment);
    Global.vars.hostlocation = voltmx.store.getItem("gHostlocation");
    if (Global.vars.hostlocation == null) {
        voltmx.print("#### Global_LoadStoredValues 1 Global.vars.hostlocation: " + Global.vars.hostlocation);
  		Global.vars.hostlocation = "l_choose";
    }
  	voltmx.print("#### Global_LoadStoredValues 2 Global.vars.hostlocation: " + Global.vars.hostlocation);
  	Global.vars.multiUserDevice = voltmx.store.getItem("MultiUserDevice");
    //voltmx.print("#### Loaded Global.vars.multiUserDevice: " + Global.vars.multiUserDevice);
    try {
        if ((Global.vars.gPassword !== undefined && Global.vars.gPassword != null) || Global.vars.multiUserDevice == "Yes") {
            //possible endpoints (moet uit service gaan komen)
            Global_getCorrectEndPoint();
            //Global_setServerInfo();
        }
        if (Global.vars.buildFor == "GEN" || Global.vars.buildFor == "RWS") {
            frmLogin_addMenuItem(voltmx.i18n.getLocalizedString("l_openTasks"));
        }
    } catch (err) {}
	voltmx.print("### Global_LoadStoredValues pinHash : <" + Global.vars.pinHash + ">");
  	
}

function Global_hdrMenu_btnMenu_onClick() {
    voltmx.print("### Global_hdrMenu_btnMenu_onClick: " + JSON.stringify(voltmx.application.getCurrentForm()));
    // execute action
    if (voltmx.application.getCurrentForm().id == "frmMenu") {
        voltmx.application.getPreviousForm().show();
    } else if (voltmx.application.getCurrentForm().id == "frmFilter") {
        voltmx.application.getPreviousForm().show();
    } else {
        Global.vars.previousForm = voltmx.application.getCurrentForm().id;
        frmMenu.show();
    }
}

function Global_hdrMenu_btnMenuSettings_onClick() {
    voltmx.print("### Global_hdrMenu_btnMenuSettings_onClick: " + JSON.stringify(voltmx.application.getCurrentForm()));
    frmMenuSetting.show();
}

function Global_exitApplication() {
    try {
        //logout identity
        voltmx.application.showLoadingScreen(lblLoader, voltmx.i18n.getLocalizedString("l_logoff") + "...", "center", true, true, {
            enablemenukey: true,
            enablebackkey: true
        });
        Global_logoutIdentity();
        if (Global.vars.buildFor !== "NS" && Global.vars.buildFor !== "GEN" && Global.vars.buildFor !== "RWS" && Global.vars.buildFor !== "OV") {
            service_LogoutAndAssignToCloud(Global_connectLogOutCallback);
        }
        voltmx.timer.schedule("ExitAPP", Global_exitAPP, 3, false);
    } catch (error) {
        voltmx.print("### Global_exitApplication - error: " + JSON.stringify(error));
        voltmx.application.dismissLoadingScreen();
    }
}

function Global_connectLogOutCallback(result) {
    voltmx.print("### Global_connectLogOutCallback result: " + JSON.stringify(result));
    if (result.opstatus === 0 && result.httpStatusCode == 200) {
        Utility_setIni("Uitgelogd");
        voltmx.application.exit();
    } else {
        Utility_setIni("Uitgelogd");
        voltmx.application.exit();
    }
}

function Global_exitAPP() {
    voltmx.print("### Utility_setIni Global_exitAPP");
    Utility_setIni("Uitgelogd");
    voltmx.application.exit();
}

function Global_onDeviceBack() {
    // do lekker niks
    voltmx.print("#### Global_onDeviceBack ####");
}
//notification handling
function Global_online_callback(notificationobject, actionid) {
    voltmx.print("### Running app callback for actionid: " + actionid);
    voltmx.print("### Online notificationobject: " + JSON.stringify(notificationobject));
    var notificationstring = notificationobject.categoryId;
    if (actionid === undefined) {
        voltmx.print("### App (nog draaiend in de achtergrond) geopend door op notificatie te klikken of app stond nog open");
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
            voltmx.application.setApplicationBadgeValue("");
        }
        frmOverview_handleNotification(notificationstring, true);
    }
}

function Global_offline_callback(notificationobject, actionid) {
    voltmx.print("### Offline app callback for actionid: " + actionid);
    voltmx.print("### Offline notificationobject: " + JSON.stringify(notificationobject));
    var notificationstring = actionid;
    if (actionid == "Open") {
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
            voltmx.application.setApplicationBadgeValue("");
        }
        frmOverview_handleNotification(notificationstring, false);
    } else if (actionid == "Later") {
        voltmx.print("### Offline Later");
        // 		if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")){
        // 			voltmx.application.setApplicationBadgeValue("");
        // 		}
        //stel de notificatie opnieuw in voor later
        localNotifications_create1NotificationsAdd30SecondsDirect(notificationstring, 0);
    } else if (actionid === undefined) {
        voltmx.print("### Offline App (niet meer draaiend) geopend door op notificatie te klikken");
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
            voltmx.application.setApplicationBadgeValue("");
        }
        frmOverview_handleNotification(notificationstring, false);
    } else if (actionid == "Negeer") { //voor nu negeer (zichtbaar op de watch) maar kan ook een ander soort actie zijn natuurlijk.
        voltmx.print("### Offline Negeer");
        if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP")) {
            voltmx.application.setApplicationBadgeValue("");
        }
        voltmx.print("### Op negeer geklikt, app wordt niet geopend maar actie wordt uitgevoerd");
        Global_handle_ignoreNotification();
        alert("U heeft gekozen de hotspot melding te negeren, deze is verwijderd uit de lijst");
    }
}

function Global_handle_ignoreNotification() {
    Global.vars.savedHotspots = voltmx.store.getItem("savedHotspots");
    if (Global.vars.savedHotspots === null || Global.vars.savedHotspots === undefined) {
        Global.vars.savedHotspots = [];
    }
    var lastsavedHotspot = "";
    if (Global.vars.savedHotspots.length > 0) {
        lastsavedHotspot = Global.vars.savedHotspots.pop();
        voltmx.store.setItem("savedHotspots", Global.vars.savedHotspots);
    }
    voltmx.print("### frmOverview_removeReport lastsavedHotspot: " + lastsavedHotspot);
    if (typeof glistitems !== "undefined") {
        for (var j in glistitems) {
            var w = glistitems[j];
            if (lastsavedHotspot === w.id) {
                glistitems.splice(j, 1);
                break;
            }
        }
    }
    voltmx.print("### frmOverview_removeReport saved hotspots: " + JSON.stringify(Global.vars.savedHotspots));
    if (Global.vars.savedHotspots.length === 0) {
        voltmx.print("### frmOverview_removeReport no more hotspots, remove notification");
        try {
            voltmx.localnotifications.cancel(["01"]);
        } catch (err) {}
    }
}

function Global_onclickSegMenu(eventobject) {
    voltmx.print("### Global_onclickSegMenu form");
    Global.vars.openedFromMenu = false;
    var form = voltmx.application.getCurrentForm().id;
    voltmx.print("### Global_onclickSegMenu form: " + form);
    voltmx.print("### Global_onclickSegMenu eventobject: " + JSON.stringify(eventobject));
    var selecteditem = eventobject.selectedItems[0];
    voltmx.print("### Global_onclickSegMenu selecteditem: " + JSON.stringify(selecteditem));
    if (form == "frmFollow") {
        voltmx.print("### Global_onclickSegMenu toggle frmFollow");
        frmFollow_toggleMenu(true);
    } else if (form == "frmTrackDown") {
        voltmx.print("### Global_onclickSegMenu toggle frmTrackDown");
        frmTrackDown_toggleMenu(true);
        if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_trackDown")) === false) {
            frmTrackDown_resetImages();
            frmTrackDown_resetForm(false);
            if(voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true){
              voltmx.print("### Global_onclickSegMenu toggle frmTrackDown iOS destroy ###");
//               frmNHA.destroy();
//               frmHandle.destroy();
//               frmResume.destroy();
            }
        }
    } else if (form == "frmCheckVehicle") {
        voltmx.print("### Global_onclickSegMenu toggle frmCheckVehicle");
        frmCheckVehicle_toggleMenu(true);
        if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_checkvehicle")) === false) {
            frmCheckVehicle_resetImages();
            frmCheckVehicle_resetForm(false);
        }
    } else if (form == "frmCheckCard") {
        voltmx.print("### Global_onclickSegMenu toggle frmCheckCard");
        frmCheckCard_toggleMenu(true);
        if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_checkcard")) === false) {
            frmCheckCard_resetForm(false);
        }
    } else if (form == "frmCheckLocation") {
        voltmx.print("### Global_onclickSegMenu toggle frmCheckLocation");
        frmCheckLocation_toggleMenu(true);
        if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_checklocation")) === false) {
            frmCheckLocation_resetForm(false);
        }
    } else if (form == "frmOpenTasks") {
        voltmx.print("### Global_onclickSegMenu toggle frmOpenTasks");
        frmOpenTasks_toggleMenu(true);
    } else if (form == "frmActiveCases") {
        voltmx.print("### Global_onclickSegMenu toggle frmActiveCases");
        frmActiveCases_toggleMenu(true);
    } else if (form == "frmOutbox") {
        voltmx.print("### Global_onclickSegMenu toggle frmOutbox");
        frmOutbox_toggleMenu(true);
    } else if (form == "frmConcepts") {
        voltmx.print("### Global_onclickSegMenu toggle frmConcepts");
        frmConcepts_toggleMenu(true);
    } else if (form == "frmHistory") {
        voltmx.print("### Global_onclickSegMenu toggle frmHistory");
        frmHistory_toggleMenu(true);
    } else if (form == "frmCardCheck") {
        voltmx.print("### Global_onclickSegMenu toggle frmCardCheck");
        frmCardCheck_toggleMenu(true);
    } else if (form == "frmStatistics") {
        voltmx.print("### Global_onclickSegMenu toggle frmStatistics");
        frmStatistics_toggleMenu(true);
    } else if (form == "frmVehicle") {
        voltmx.print("### Global_onclickSegMenu toggle frmVehicle");
        frmVehicle_toggleMenu(true);
    } else if (form == "frmLogOn") {
        voltmx.print("### Global_onclickSegMenu toggle frmLogOn");
        frmLogOn_toggleMenu(true);
    } else if (form == "frmRegister") {
        voltmx.print("### Global_onclickSegMenu toggle frmRegister");
        frmRegister_toggleMenu(true);
        if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_register")) === false) {
            frmRegister_resetForm(false);
        }
    } else if (form == "frmRegisterConcept") {
        voltmx.print("### Global_onclickSegMenu toggle frmRegisterConcept");
        frmRegisterConcept_toggleMenu(true);
        if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_registerconcept")) === false) {
            frmRegisterConcept_resetForm(false);
        }
    } else if (form == "frmProhibitions") {
        voltmx.print("### Global_onclickSegMenu toggle frmProhibitions");
        frmProhibitions_toggleMenu(true);
    } else if (form == "frmRegisterLabel") {
        voltmx.print("### Global_onclickSegMenu toggle frmRegisterLabel");
        frmRegisterLabel_toggleMenu(true);
    } else if (form == "frmCheckLabel") {
        voltmx.print("### Global_onclickSegMenu toggle frmCheckLabel");
        frmCheckLabel_toggleMenu(true);
    } else if (form == "frmOverviewLabel") {
        voltmx.print("### Global_onclickSegMenu toggle frmOverviewLabel");
        frmOverviewLabel_toggleMenu(true);
    } else if (form == "frmOverviewTask") {
        voltmx.print("### Global_onclickSegMenu toggle frmOverviewTask");
        frmOverviewTask_toggleMenu(true);
      	if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_overviewTask")) === false) {
          	frmOverviewTask_resetForm();
        }
    }
    if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_followUp"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_followUp"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("l_followUp");
        if (form != "frmFollow") {
            frmFollow.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_trackDown"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_trackDown"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("l_trackDown");
        if (form != "frmTrackDown") {
            frmTrackDown_resetForm();
            frmTrackDown.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_checkvehicle"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_checkvehicle"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("l_checkvehicle");
        if (form != "frmCheckVehicle") {
            frmCheckVehicle_resetForm();
            frmCheckVehicle.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_checkcard"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_checkcard"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("l_checkcard");
        if (form != "frmCheckCard") {
            frmCheckCard_resetForm();
            frmCheckCard.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_checklocation"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_checklocation"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("l_checklocation");
        if (form != "frmCheckLocation") {
            frmCheckLocation_resetForm();
            frmCheckLocation.show();
        }
    } else if (selecteditem.lblMenuItem == voltmx.i18n.getLocalizedString("l_register")) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_register"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("l_register");
        if (form != "frmRegister") {
            frmRegister.show();
        }
    } else if (selecteditem.lblMenuItem == voltmx.i18n.getLocalizedString("l_registerconcept")) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_registerconcept"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("appmode_registerconcept");
        if (form != "frmRegisterConcept") {
            frmRegisterConcept.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_openTasks"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_openTasks"), true);
        if (form != "frmOpenTasks") {
            frmOpenTasks.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_activeCases"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_activeCases"), true);
        if (form != "frmActiveCases") {
            frmActiveCases.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_concepts"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_concepts"), true);
        if (form != "frmConcepts") {
            frmConcepts.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_history"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_history"), true);
        if (form != "frmHistory") {
            frmHistory.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_cardcheck"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_cardcheck"), true);
        if (form != "frmCardCheck") {
            frmCardCheck.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_outbox"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_outbox"), true);
        if (form != "frmOutbox") {
            frmOutbox.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_statistics"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_statistics"), true);
        if (form != "frmStatistics") {
            frmStatistics.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_vehicle"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_vehicle"), true);
        if (form != "frmVehicle") {
          Global.vars.openedFromMenu = true;
          Global.vars.previousMenuForm = voltmx.application.getCurrentForm().id;
          CaseData.vehicle[Global.vars.gCaseVehiclesIndex] = CaseData_setNewvehicle();
          CaseData.vehicle[Global.vars.gCaseVehiclesIndex].identType = vehicleIdentType.vehicle;
          CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicense = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryModule = Global.vars.licenseplateCountryModule;
          CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseCode = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryCode = Global.vars.licenseplateCountryCode;
          CaseData.vehicle[Global.vars.gCaseVehiclesIndex].countryLicenseDesc = Global.vars.lastChosenLicensePlateCountry.licenseplateCountryDesc =Global.vars.licenseplateCountryDesc;
          Global.vars.gCaseVehicles = JSON.parse(JSON.stringify(CaseData.vehicle[Global.vars.gCaseVehiclesIndex]));
          voltmx.print("### Global_onclickSegMenu toggle frmVehicle Global.vars.gCaseVehicles: " + JSON.stringify(Global.vars.gCaseVehicles));
          frmVehicle.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_person"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_person"), true);
        if (form != "frmPerson") {
            Global.vars.openedFromMenu = true;
            Global.vars.previousMenuForm = voltmx.application.getCurrentForm().id;
            frmPerson.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_login")) || (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_logoff")))) {
        if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_logoff"))) {
            Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_logoff"), true);
            frmLogOn.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_logoff");
        } else {
            Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_login"), true);
            frmLogOn.lblSubHeader.text = voltmx.i18n.getLocalizedString("l_login");
        }
        if (form != "frmLogOn") {
            frmLogOn.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_prohibitions"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_prohibitions"), true);
        if (form != "frmProhibitions") {
            frmProhibitions.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_addLabel"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_addLabel"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("appmode_registerlabel");
        if (form != "frmRegisterLabel") {
            CaseData_init();
            frmRegisterLabel_resetForm();
            frmRegisterLabel.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_labelCheck"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_labelCheck"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("appmode_checklabel");
        if (form != "frmCheckLabel") {
            CaseData_init();
            frmCheckLabel_resetForm();
            frmCheckLabel.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_overviewLabel"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_overviewLabel"), true);
        Global.vars.appMode = voltmx.i18n.getLocalizedString("appmode_labeloverview");
        if (form != "frmOverviewLabel") {
            frmOverviewLabel.show();
            //frmCheckLabel_resetForm();
            //frmCheckLabel.show();
        }
    } else if (voltmx.string.startsWith(selecteditem.lblMenuItem, voltmx.i18n.getLocalizedString("l_overviewTask"))) {
        Global_setSelectedMenuItem(voltmx.i18n.getLocalizedString("l_overviewTask"), true);
        //Global.vars.appMode = voltmx.i18n.getLocalizedString("appmode_tasksoverview");
        Global.vars.appMode = voltmx.i18n.getLocalizedString("l_followUp");
        if (form != "frmOverviewTask") {
            frmOverviewTask.show();
            //frmCheckLabel_resetForm();
            //frmCheckLabel.show();
        }
    }
    voltmx.print("### Global_onclickSegMenu form 2: " + form);
}

function Global_setSelectedMenuItem(item, setselector) {
    voltmx.print("### Global_setSelectedMenuItem item: " + item + " setselector: " + setselector);
    for (var i in Global.vars.menudata) {
        var v = Global.vars.menudata[i];
        if (voltmx.string.startsWith(v.lblMenuItem, item) === true && setselector === true) {
            voltmx.print("### Global_setSelectedMenuItem set selector for item: " + item);
            v.imgSelector = "menuselect.png";
        } else if (voltmx.string.startsWith(v.lblMenuItem, item) === false && setselector === true) {
            v.imgSelector = "menuunselect.png";
        }
        if (item == voltmx.i18n.getLocalizedString("l_logoff") && v.lblMenuItem == voltmx.i18n.getLocalizedString("l_login")) {
            v.lblMenuItem = voltmx.i18n.getLocalizedString("l_logoff");
        } else if (item == voltmx.i18n.getLocalizedString("l_login") && v.lblMenuItem == voltmx.i18n.getLocalizedString("l_logoff")) {
            v.lblMenuItem = voltmx.i18n.getLocalizedString("l_login");
        }
    }
    var form = voltmx.application.getCurrentForm().id;
    if (form == "frmFollow") {
        frmFollow.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmTrackDown") {
        frmTrackDown.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmCheckVehicle") {
        frmCheckVehicle.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmCheckCard") {
        frmCheckCard.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmCheckLocation") {
        frmCheckLocation.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmActiveCases") {
        frmActiveCases.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmStatistics") {
        frmStatistics.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmOutbox") {
        frmOutbox.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmConcepts") {
        frmConcepts.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmLogOn") {
        frmLogOn.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmRegister") {
        frmRegister.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmOpenTasks") {
        frmOpenTasks.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmProhibitions") {
        frmProhibitions.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmHistory") {
        frmHistory.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmCardCheck") {
        frmCardCheck.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmVehicle") {
        frmVehicle.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmRegisterLabel") {
        frmRegisterLabel.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmRegisterConcept") {
        frmRegisterConcept.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmCheckLabel") {
        frmCheckLabel.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmOverviewLabel") {
        frmOverviewLabel.menu2.segMenu.setData(Global.vars.menudata);
    } else if (form == "frmOverviewTask") {
        frmOverviewTask.menu2.segMenu.setData(Global.vars.menudata);
    }
    Utility_countFilelists();
}

function Global_updateMenuCounters() {
    if (voltmx.string.startsWith(Global.vars.gDeviceInfo.name, "iP") === true) { //iphone continues
        voltmx.runOnMainThread(Global_updateMenuCounters_orig, []);
    } else {
        Global_updateMenuCounters_orig();
    }
}

function Global_updateMenuCounters_orig() {
    voltmx.print("### Global_updateMenuCounters");
    if (Global.vars.hideZeroCounters === true) {
        if (Global.vars.numberOfOutboxfiles !== undefined) {
            if (typeof Global.vars.numberOfOutboxfiles != 'number') {
                Global.vars.numberOfOutboxfiles = 0;
            }
            if (Number(Global.vars.numberOfOutboxfiles) > 0) {
                frmLogin_addMenuItem(voltmx.i18n.getLocalizedString("l_outbox"));
            } else {
                frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_outbox"));
            }
        } else {
            frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_outbox"));
        }
        if (Global.vars.numberOfOutboxfiles !== undefined) {
            if (typeof Global.vars.numberOfOutboxfiles != 'number') {
                Global.vars.numberOfOutboxfiles = 0;
            }
            if (Number(Global.vars.numberOfOutboxfiles) > 0) {
                frmLogin_addMenuItem(voltmx.i18n.getLocalizedString("l_outbox"));
            } else {
                frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_outbox"));
            }
        } else {
            frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_outbox"));
        }
        if (Global.vars.numberOfConceptfiles !== undefined || Global.vars.numberOfConceptfilesOnDevice !== undefined) {
            if (typeof Global.vars.numberOfConceptfiles != 'number') {
                Global.vars.numberOfConceptfiles = 0;
            }
            if (typeof Global.vars.numberOfConceptfilesOnDevice != 'number') {
                Global.vars.numberOfConceptfiles = 0;
            }
            if (Number(Global.vars.numberOfConceptfiles) > 0 || Number(Global.vars.numberOfConceptfilesOnDevice) > 0) {
                frmLogin_addMenuItem(voltmx.i18n.getLocalizedString("l_concepts"));
            } else {
                frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_concepts"));
            }
        } else {
            frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_concepts"));
        }
        if (Global.vars.numberOfopenTasks !== undefined) {
            if (typeof Global.vars.numberOfopenTasks != 'number') {
                Global.vars.numberOfopenTasks = 0;
            }
            if (Number(Global.vars.numberOfopenTasks) > 0) {
                frmLogin_addMenuItem(voltmx.i18n.getLocalizedString("l_openTasks"));
            } else {
                frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_openTasks"));
            }
        } else {
            frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_openTasks"));
        }
    } else if (Global.vars.saveCaseEnabled === false) {
        if (Global.vars.numberOfConceptfiles !== undefined || Global.vars.numberOfConceptfilesOnDevice !== undefined) {
            if (typeof Global.vars.numberOfConceptfiles != 'number') {
                Global.vars.numberOfConceptfiles = 0;
            }
            if (typeof Global.vars.numberOfConceptfilesOnDevice != 'number') {
                Global.vars.numberOfConceptfilesOnDevice = 0;
            }
            if (Number(Global.vars.numberOfConceptfiles) > 0 || Number(Global.vars.numberOfConceptfilesOnDevice) > 0) {
                frmLogin_addMenuItem(voltmx.i18n.getLocalizedString("l_concepts"));
            } else {
                frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_concepts"));
            }
        } else {
            frmLogin_deleteMenuItem(voltmx.i18n.getLocalizedString("l_concepts"));
        }
    }
    for (var i in Global.vars.menudata) {
        var v = Global.vars.menudata[i];
        if (voltmx.string.startsWith(v.lblMenuItem, voltmx.i18n.getLocalizedString("l_outbox"))) {
            if (Global.vars.numberOfOutboxfiles !== undefined) {
                v.lblMenuItem = voltmx.i18n.getLocalizedString("l_outbox") + " (" + Global.vars.numberOfOutboxfiles + ")";
            }
        } else if (voltmx.string.startsWith(v.lblMenuItem, voltmx.i18n.getLocalizedString("l_concepts"))) {
            if (Global.vars.numberOfConceptfiles !== undefined) {
                v.lblMenuItem = voltmx.i18n.getLocalizedString("l_concepts") + " (" + (Number(Global.vars.numberOfConceptfiles) + Number(Global.vars.numberOfConceptfilesOnDevice)).toString() + ")";
            }
        } else if (voltmx.string.startsWith(v.lblMenuItem, voltmx.i18n.getLocalizedString("l_openTasks"))) {
            if (Global.vars.numberOfopenTasks !== undefined) {
                if (typeof Global.vars.numberOfopenTasks != 'number') {
                    Global.vars.numberOfopenTasks = 0;
                }
                v.lblMenuItem = voltmx.i18n.getLocalizedString("l_openTasks") + " (" + Global.vars.numberOfopenTasks + ")";
            }
        }
    }
    var form = voltmx.application.getCurrentForm().id;
    voltmx.print("### Global_updateMenuCounters form: " + form);
    if (Global.vars.numberOfConceptfiles !== undefined) {
        voltmx.print("### Global_updateMenuCounters concepts: " + (Number(Global.vars.numberOfConceptfiles)));
    }
    try {
        if (form == "frmFollow") {
//             frmFollow.menu2.segMenu.setData(Global.vars.menudata);
//            voltmx.runOnMainThread(frmFollow_setMenuCounter, []);
        } else if (form == "frmTrackDown") {
          	voltmx.runOnMainThread(frmTrackDown_setMenuCounter, []);
        } else if (form == "frmCheckVehicle") {
//             frmCheckVehicle.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmCheckVehicle_setMenuCounter, []);
        } else if (form == "frmCheckCard") {
//             frmCheckCard.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmCheckCard_setMenuCounter, []);
        } else if (form == "frmCheckLocation") {
//             frmCheckLocation.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmCheckLocation_setMenuCounter, []);
        } else if (form == "frmActiveCases") {
//             frmActiveCases.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmActiveCases_setMenuCounter, []);
        } else if (form == "frmStatistics") {
//             frmStatistics.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmStatistics_setMenuCounter, []);
        } else if (form == "frmOutbox") {
//             frmOutbox.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmOutbox_setMenuCounter, []);
        } else if (form == "frmConcepts") {
//             frmConcepts.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmConcepts_setMenuCounter, []);
        } else if (form == "frmLogOn") {
//             frmLogOn.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmLogOn_setMenuCounter, []);
        } else if (form == "frmRegister") {
//             frmRegister.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmRegister_setMenuCounter, []);
        } else if (form == "frmRegisterConcept") {
//             frmRegisterConcept.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmRegisterConcept_setMenuCounter, []);
        } else if (form == "frmOpenTasks") {
//             frmOpenTasks.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmOpenTasks_setMenuCounter, []);
        } else if (form == "frmProhibitions") {
//             frmProhibitions.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmProhibitions_setMenuCounter, []);
        } else if (form == "frmRegisterLabel") {
//             frmRegisterLabel.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmRegisterLabel_setMenuCounter, []);
        } else if (form == "frmCheckLabel") {
//             frmCheckLabel.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmCheckLabel_setMenuCounter, []);
        } else if (form == "frmOverviewLabel") {
//             frmOverviewLabel.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmOverviewLabel_setMenuCounter, []);
        } else if (form == "frmOverviewTask") {
//             frmOverviewTask.menu2.segMenu.setData(Global.vars.menudata);
            voltmx.runOnMainThread(frmOverviewTask_setMenuCounter, []);
        }
    } catch (err) {
        voltmx.print("### Global_updateMenuCounters catch: " + Global.vars.retrySetMenu);
    }
}

function Global_onclick_segmentMenuSettings(eventobject) {
    voltmx.print("### Global_onclick_segmentMenuSettings eventobject: " + eventobject);
    var form = voltmx.application.getCurrentForm().id;
    var selecteditem = eventobject.selectedItems[0];
    Global.vars.backFromMenuSettings = true;
    Global.vars.previousForm = "" + voltmx.application.getCurrentForm().id;
    voltmx.print("### Global_onclick_segmentMenuSettings form: " + form);
    if (selecteditem.lblMenuSettingsItem == "Teamselect") {
        frmTeamSelect.show();
    } else if (selecteditem.lblMenuSettingsItem == "CouchDB") {
        frmCouchSync.show();
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_about")) {
        Global_onclick_segmentMenusettingsSetTransitions(form);
        frmInfo.show();
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_synchronization")) {
        Global.vars.LoggedIn = false;
        Global_onclick_segmentMenusettingsSetTransitions(form);
        frmSynchronization.show();
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_register")) {
        Global_onclick_segmentMenusettingsSetTransitions(form);
        frmSyncInit.show();
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_selectPrinter")) {
        frmPrinterSelect.show();
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_exit")) {
        Global_exitApplication();
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_resetPin")) {
        Global.vars.resetPin = true;
        frmPinLogin.show();
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_createPin")) {
        Global.vars.createPinAfter = true;
        frmPinLogin.show();
    } else if (selecteditem.lblMenuSettingsItem == "Mulder") {
        frmFollow_toggleMenuSettings();
        frmFollow_toggleMenu();
        //Load existing CaseData
        frmFollow_showDemoDataMulder(); //TESTDATA
    } else if (selecteditem.lblMenuSettingsItem == "Fiscaal") {
        frmFollow_toggleMenuSettings();
        frmFollow_toggleMenu();
        //Load existing CaseData
        frmFollow_showDemoDataFiscaal(); //TESTDATA
    } else if (selecteditem.lblMenuSettingsItem == voltmx.i18n.getLocalizedString("l_preferences")) {
        Global_onclick_segmentMenusettingsSetTransitions(form);
        frmPreferences.show();
    } else if (selecteditem.lblMenuSettingsItem == "PDF") {
        Global_onclick_segmentMenusettingsSetTransitions(form);
        frmPDF.show();
    } else if (selecteditem.lblMenuSettingsItem == "FAQ") {
        frmFaqNS.show();
    } else if (selecteditem.lblMenuSettingsItem == "Read ID") {
        frmReadID.show();
    }
}

function Global_onclick_segmentMenusSetTransitions(form) {
    if (Global.vars.gDeviceInfo.name == "iPhone") {
        if (form == "frmFollow") {
            frmFollow.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmFollow.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmTrackDown") {
            frmTrackDown.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmTrackDown.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckVehicle") {
            frmCheckVehicle.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckVehicle.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckCard") {
            frmCheckCard.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckCard.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckLocation") {
            frmCheckLocation.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckLocation.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmActiveCases") {
            frmActiveCases.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmActiveCases.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmStatistics") {
            frmStatistics.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmStatistics.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmLogOn") {
            frmLogOn.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmLogOn.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmConcepts") {
            frmConcepts.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmConcepts.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmOutbox") {
            frmOutbox.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmOutbox.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmRegister") {
            frmRegister.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmRegister.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmRegisterLabel") {
            frmRegisterLabel.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmRegisterLabel.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckLabel") {
            frmCheckLabel.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckLabel.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmOverviewLabel") {
            frmOverviewLabel.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmOverviewLabel.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmOverviewTask") {
            frmOverviewTask.inTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
            frmOverviewTask.outTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
        }
    }
}

function Global_onclick_segmentMenusettingsSetTransitions(form) {
    if (Global.vars.gDeviceInfo.name == "iPhone") {
        if (form == "frmFollow") {
            frmFollow.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmFollow.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmTrackDown") {
            frmTrackDown.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmTrackDown.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckVehicle") {
            frmCheckVehicle.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckVehicle.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckCard") {
            frmCheckCard.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckCard.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckLocation") {
            frmCheckLocation.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckVehicle.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmActiveCases") {
            frmActiveCases.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmActiveCases.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmStatistics") {
            frmStatistics.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmStatistics.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmLogOn") {
            frmLogOn.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmLogOn.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmConcepts") {
            frmConcepts.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmConcepts.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmOutbox") {
            frmOutbox.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmOutbox.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmRegister") {
            frmRegister.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmRegister.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmRegisterLabel") {
            frmRegisterLabel.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmRegisterLabel.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmCheckLabel") {
            frmCheckLabel.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmCheckLabel.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmOverviewLabel") {
            frmOverviewLabel.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmOverviewLabel.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        } else if (form == "frmOverviewTask") {
            frmOverviewTask.inTransitionConfig = {
                "transitionDirection": "fromLeft",
                "transitionEffect": "transitionMoveIn"
            };
            frmOverviewTask.outTransitionConfig = {
                "transitionDirection": "fromRight",
                "transitionEffect": "transitionMoveIn"
            };
        }
    }
}

function Global_setButtonLogoffMenu() {
    if (Global.vars.multiUserDevice == "Yes") {
        menus.menu2.showFooterMenuButtons = true;
    } else {
        menus.menu2.showFooterMenuButtons = false;
    }
}

function Global_resetApp() {
    voltmx.print("### Global_resetApp");
    CaseData_init();
    frmOffenceSelect_clearSearchText();
    //Globals
    Global_resetGlobals();
    //logout identity
    Global_logoutIdentity();
    //specific
    try {
        frmFollow_resetImages();
        frmFollow_resetForm();
        frmTrackDown_resetForm(false);
        frmRegister_resetForm(false);
        //frmRegisterConcept_resetForm(false);
        frmRegisterLabel_reset();
        frmCheckLabel_reset();
        //frmCheckCard_resetForm(false);
        //frmCheckLocation_resetForm(false);
        //frmCheckVehicle_resetForm(false);
        //frmFollow_flcGetNewCaseList_setVisibility(true);
        //frmFollow.mapCases.clear();
        frmOverviewTask.mapCases.clear();
      	frmResume_resetAllFields();
        frmClamp_resetAllFields();
        frmActiveCaseResume_resetAllFields();
        frmActiveCaseResume_resetImages();
        voltmx.print("### Global_resetApp frmLocation.mapLocation.clear");
        frmLocation.mapLocation.clear();
        frmLocation.segAreas.removeAll();
    } catch (err) {}
}

function Global_logoutIdentity(callback, errorCallback) {
  voltmx.print("### Global_logoutIdentity");
  function logoutIdentityCallback(){
    voltmx.print("### Global_logoutIdentity logoutIdentityCallback");
  	//Global.vars.gPassword = "";
    //Utility_storeSetItem("password", Global.vars.gPassword);
	Global.vars.loggedOut = "yes";
    voltmx.store.setItem("loggedOut", Global.vars.loggedOut);
    callback();
  }
  if (callback != null) {
    // RKA RL-7926  && callback != null toegevoegd, alleen uitloggen met een callback 
    voltmx.print("### Global_logoutIdentity gIdentityProvider" + Global.vars.gIdentityProvider);
    Global.vars.useMobileAppAzureAD = true;
    if (Global.vars.resetUserViaLogOff === true){
      identityService_logout(Global.vars.gIdentityProvider, frmInfo_clearUserAndDataStoreItemsCallback,frmInfo_clearUserAndDataStoreItemsErrorCallback);
    } else {
      identityService_logout(Global.vars.gIdentityProvider, logoutIdentityCallback, errorCallback);

    }
  }
}

function Global_getCorrectEndPoint() {
    voltmx.print("### Global_getCorrectEndPoint for Global.vars.hostlocation: " + Global.vars.hostlocation);
    //GEN ENDPOINTS
    if (Global.vars.hostlocation == "eu-central-1.rl" || Global.vars.hostlocation == "l_choose") {
        Global.vars.endpoints = [{
            environment: "development",
            appKey: "4ZmX4ZaW4qCc4Yy64Yy84Y6a4Y2m4YuT4Y+P4ZCD4pin4ZOF4pyo4Y6c4pmi4ZOG",
            appSecret: "4pmg4ZmV4Y6d4pux4ZO44p2T4pyk4puD4Ze04YyC4Zmb4Zin4ZWc4ZmX4ZGq4pyn",
            endpoint: "4qO14rah4rSH4Yms4rmo4qqF4qut4Yeb4q6y4qy94rOi4Yi94p+L4Yi+4reC4puy4q2N4rOK4qi+4Yyz4rOV4YmN4ruW4q2M4Yi84q6q4Yqe4riJ4qOU4p+H4rmi4puy4Ymu4YuR4YuR4YuR4YuT4Yqe4rCR4pu84qy/4qWM",
            name: "VoltMX Dev"
        }, {
            environment: "test",
            appKey: "4pe44YuZ4Y6i4pyi4p2S4Y+P4ZOO4puB4ZWe4Zme4Y+M4ZmX4Zez4ZCz4pyl4p+q",
            appSecret: "4Zez4ZGp4puB4puz4qCd4ZaU4YyD4ZSs4ZGj4YuR4p6L4Y6g4ZSw4Za/4ZSv4Zez",
            endpoint: "4qO14rah4rSH4Yms4rmo4qqF4qut4Yeb4q6y4qy94rOi4YmN4rWB4Yi+4reC4puy4q2N4rOK4qi+4Yyz4rOV4YmN4ruW4q2M4Yi84q6q4Yqe4riJ4qOU4p+H4rmi4puy4Ymu4YuR4YuR4YuR4YuT4Yqe4rCR4pu84qy/4qWM",
            name: "VoltMX Test"
        }, {
            environment: "acceptance",
            appKey: "4p6M4pyh4qCb4ZCx4p+q4Ze14ZGo4ZmV4ZWc4ZaP4p644Y6d4ZCv4ZOM4ZCD4ZWb",
            appSecret: "4pe54pme4pqK4p+v4p6L4ZaP4puz4pe54YyF4Y+N4Zim4ZmV4ZSt4Y+O4p6F4pe4",
            endpoint: "4qO14rah4rSH4Yms4rmo4qqF4qut4Yeb4q6y4qy94rOi4Yi64puw4Yi+4reC4puy4q2N4rOK4qi+4Yyz4rOV4YmN4ruW4q2M4Yi84q6q4Yqe4riJ4qOU4p+H4rmi4puy4Ymu4YuR4YuR4YuR4YuT4Yqe4rCR4pu84qy/4qWM",
            name: "Generic Acc"
        }, {
            environment: "production",
            appKey: "4pyn4ZON4YyE4ZCA4ZCx4Zey4ZSw4Yy54ZGh4ZSr4pmd4p2U4Zmb4Y+M4Y6e4ZOK",
            appSecret: "4pme4Yua4Ze44ZmW4ZWd4p+x4ZKU4p+v4YuY4ZGi4Y6i4p+v4ZWe4pe34Zmc4puz",
            endpoint: "4qO14rah4rSH4Yms4rmo4qqF4qut4Yeb4q6y4qy94rOi4YmJ4rON4Yi+4reC4puy4q2N4rOK4qi+4Yyz4rOV4YmN4ruW4q2M4Yi84q6q4Yqe4riJ4qOU4p+H4rmi4puy4Ymu4YuR4YuR4YuR4YuT4Yqe4rCR4pu84qy/4qWM",
            name: "Generic Prd"
        }, ];
    } else if (Global.vars.hostlocation == "eu-central-1.pt") {
        Global.vars.endpoints = [{
            environment: "acceptance",
            appKey: "4ZOO4Y6c4Zik4Ze34ZCx4p6H4ZOG4ZGn4ZaQ4pq+4ZCG4Y6b4puz4p6N4qCd4Zmb",
            appSecret: "4ZCz4pe34p+t4puG4ZCC4pyl4p6L4ZSr4qCc4Yy34ZmV4ZO44YyF4ZCy4Y2qOA==",
            endpoint: "4qO14rah4rSH4Yms4rmo4qqF4qut4Yeb4q6y4qy94rOi4Yi64puw4Yi+4reC4puy4q2N4rOK4qi+4Yyz4rCV4YmN4ruW4q2M4Yi84q6q4Yqe4riJ4qOU4p+H4rmi4puy4Ymu4YuR4YuR4YuR4YuT4Yqe4rCR4pu84qy/4qWM",
            name: "PT Acc"
        }, {
            environment: "production",
            appKey: "4pip4pin4Y6e4Y+/4ZWc4p654YyD4ZOH4puw4ZGq4p6N4pip4YuS4ZSt4Y+P4pyl",
            appSecret: "4ZCC4pq+4p2X4ZmV4p+r4YuY4Zez4Y6e4p2U4ZeA4p6H4pe54ZWc4ZKX4YuV4pma",
            endpoint: "4qO14rah4rSH4Yms4rmo4qqF4qut4Yeb4q6y4qy94rOi4YmJ4rON4Yi+4reC4puy4q2N4rOK4qi+4Yyz4rCV4YmN4ruW4q2M4Yi84q6q4Yqe4riJ4qOU4p+H4rmi4puy4Ymu4YuR4YuR4YuR4YuT4Yqe4rCR4pu84qy/4qWM",
            name: "PT Prd"
        }, ];
    }
}

function Global_resetGlobals() {
    voltmx.print("### Global_resetGlobals");
    Global.vars.newPhotos = [];
    Global.vars.getPhotos = [];
    Global.vars.onlineAttachments = [];
    Global.vars.attachmentsLoaded = false;
    Global.vars.addPhotos = [];
    Global.vars.anprCalled = false;
    Global.vars.addANPRPhotos = [];
    Global.vars.showPhotosANPRLoaded = false;
    Global.vars.sendPhotosANPR = false;
    Global.vars.photosLoaded = false;
    Global.vars.previousForm = null;
    Global.vars.openedFromResume = false;
    Global.vars.openedFromResumePreviousForm = null;
    Global.vars.timeSetCircle = false;
    Global.vars.claimedDoc = {};
    Global.vars.originalCaseInfo = {};
    Global.vars.serviceTaskValues = {};
    Global.vars.taskRemark = "";
    Global.vars.claimedDocID = null;
    Global.vars.clampCloudId = null;
    Global.vars.triedGettingClampResults = false;
    Global.vars.noClampCasesFound = false;
    Global.vars.newTimeSet = false;
    Global.vars.questionTypeChooseEnabled = false;
    if (Global.vars.savedLocation === null) {
        Global.vars.newLocationSet = false;
        Global.vars.savedLocationManual = null;
    }
    Global.vars.gCasePersons = {};
    Global.vars.originalPersonDocumentInfo = {};
    Global.vars.gCaseVehicles = CaseData_setNewvehicle();
    Global.vars.gCaseParkings = CaseData_setNewParking();
    Global.vars.optionvariablesText = "";
    Global.vars.optionvariablesSet = false;
    Global.vars.CaseDataFromFile = null;
    Global.vars.writtenCaseName = "";
    Global.vars.movedCase = "";
    Global.vars.couchCaseUpdateMultimediaCallback = null;
    Global.vars.createCaseRetry = 0;
    Global.vars.needToUploadPhotos = false;
    Global.vars.offlinePhotosToUpload = [];
    Global.vars.caseOpenedFromConcepts = false;
    Global.vars.selectedCaseIndex = null;
    //Global.vars.numberOfConceptfiles = 0;
    //Global.vars.numberOfOutboxfiles = 0;
    Global.vars.profile = {
        full_name: "",
        u_id: "",
        p_p: null,
        first_name: "",
        last_name: "",
        email: ""
    };
    Global.vars.microsoftAccessToken = null;
    //Global.vars.endpoints = [];
    Global.vars.trainstationSelect = null;
    Global.vars.selectedOption = {};
    Global.vars.toOptionsFromForm = "";
    Global.vars.toStatementFromForm = "";
    Global.vars.seeAndEditCompleteOptionTextEditTicket = false;
    Global.vars.readCompleteOptionText = false;
    Global.vars.fillAndEditOptionData = false;
    Global.vars.copyPasteCompleteOptionText = false;
    Global.vars.afkortingStation = "";
    Global.vars.hectometerRaaiValidated = false;
    Global.vars.checkdocument = "";
    Global.vars.indDcocumentChecked = null;
    Global.vars.indDcocumentValidated = null;
    Global.vars.documentTypeCheckable = null;
    Global.vars.processinfo_lastTaskProcessed = {};
    Global.vars.taskTypeId = "";
    Global.vars.taskType = "";
    Global.vars.taskTypeProcess = "";
    Global.vars.tasktypeBreadCrumb = [];
    Global.vars.getOutcometypes = {};
    Global.vars.selectedOutcome = null;
    //Global.vars.stations = [];
    Global.vars.chosenCity = "";
    Global.vars.Personsaddresses = {
        country: null,
      	countryCode: null,
      	street: null,
        streetNumber: null,
        streetNumAdditn: null,
        zipcode: null,
        city: null,
        cityCode: null,
        addressline1: null,
        addressline2: null,
        addressline3: null
    };
    Global.vars.documentCountryPersonManual = false;
    Global.vars.environmentName = "";
    Global.vars.handleCharacteristicType = "";
    Global.vars.numberOfFailedSyncs = 0;
    Global.vars.objectReSync = false;
    Global.vars.readIDScanned = false;
    Global.vars.LocationCityCategory = "municipality";
    Global.vars.chosenMunicipality = null;
    //
    Global.vars.tariffNHA = "0.00";
    //	
    Global.vars.germanCarFormatted = false;
    Global.vars.carChecked = false;
    Global.vars.gSchedules = [];
    //
    Global.vars.preserveData = {};
    Global.vars.preserve = [];
  	//
    Global.vars.unknownVehicleCountry = false;
    //
    Global.vars.gCaseEnforcementObject = null;
    Global.vars.questionTypes = [];
    Global.vars.questionTypesUsage = "";
    Global.vars.questions = [];
    Global.vars.selectedQuestionTypes = [];
    Global.vars.selectedQuestionType = null;
    Global.vars.selectedQuestionIndex = null;
    Global.vars.QuestionsSet = false;
    Global.vars.caseTypeDescription = "";
    //Global.vars.selectedAreas = null;
    Global.vars.busOrTrainstationSelect = null;
    Global.vars.trainRideAmount = 0;
    Global.vars.busstationSelect = null;
    Global.vars.caseTypeCategoryDescription = "";
    Global.vars.prohibitionsStationSearch = false;
    Global.vars.currentPersonInformationProhibitions = [];
    Global.vars.stationSearchProhibitions = "";
    Global.vars.stationCodeSearchProhibitions = "";
    Global.vars.saveLocationForProhibition = null;
    Global.vars.prohibitionsFillPoliceOfficerEmailFromCase = true;
    Global.vars.prohibitionsFillPoliceOfficerMobileFromCase = true;
    //
    Global.vars.maxoccurences = 0;
    Global.vars.originalOffenceAmounts = {
        "amount": null,
        "amountDisplay": null,
        "amountExtra": null,
        "amountExtraDisplay": null
    };
    Global.vars.maxWarningsReached = false;
    //
    Global.vars.followCasesBookmark = null;
    //
    Global.vars.registerLabelCaseLoaded = false;
    Global.vars.labelRegisteredLocation = null;
    Global.vars.labelRegisteredTime = null;
    Global.vars.labelCasesBookmark = null;
    Global.vars.currentLabelPhotos = 0;
    Global.vars.fromOverViewLabel = false;
    Global.vars.cameToVehicleFromForm = "";
    Global.vars.cameToLocationFromForm = "";
    Global.vars.cameToEnforcementObjectFromForm = "";
    Global.vars.cameToTrainRideFromForm = "";
    Global.vars.cameToBusRideFromForm = "";
    Global.vars.cameToOnBusStationFromForm = "";
    Global.vars.cameToTrainTrackFromForm = "";
    Global.vars.cameToOnStationFromForm = "";
    Global.vars.cameToRegisterResumeFromForm = "";
    Global.vars.cameToCaseTypeSelectFromForm = "";
    Global.vars.cameBackFromOffenceSelect = false;
    Global.vars.directOffenceSelect = false;
    Global.vars.inCorrectUserPopupShowed = false;
    Global.vars.generalAlertRaised = false;
    Global.vars.VteCode = null;
    Global.vars.ageCategory = null;
    Global.vars.taskTypesToQueryInitiatorOnly = '';
    // validate Case on RegisterResume
    Global.vars.missingOffenceData = false;
    Global.vars.authorizedTicketType = false;
    Global.vars.authorizedTicketTypeInList = true;
    //
    Global.vars.retryRetrieveCase = 0;
    Global.vars.indCommentrequired = false;
    Global.vars.isNumberCheckOnAddressline2 = true;
  	Global.vars.markertakenImage = "";
    Global.vars.externalDocumentScan = false;
}

function deepLink(params){
  voltmx.print("### Deeplink params: "+params);
  Global.vars.afterDeeplink = true;
  if(params && params.launchmode==3)
  {
    voltmx.print("### deepLink params.launchparams.URL: " + params.launchparams.URL);
    if(params.launchparams.data != null && params.launchparams.data.includes("#MRZDATA#")) {
      Analytics_logEvent("read_id", "document_scanned");
      voltmx.print("### After Scan MRZ with data: " + params.launchparams.data);
      Global.vars.externalDocumentScan = true;
      voltmx.runOnMainThread(frmPerson_MRZ_resultcallback, [params.launchparams.data]);
    } else if(params.launchparams.nfcData != null) {
      voltmx.print("### After NFC with data: " + params.launchparams.nfcData);
      Global.vars.externalDocumentScan = true;
      voltmx.runOnMainThread(frmPerson_resultcallbackNFC, [params.launchparams.nfcData]);
    } else {
      handleDeeplinkCallback(params);
    }
  }
  return null;
}
