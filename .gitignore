# VoltMX Iris specific files
*.kar
*.ipa
*.apk
*.aab
binaries/
build/
dist/
temp/
.temp/

# Build automation and certificates
buildAutomation/
certificates/
*.keystore
*.p12
*.pem
*.dat
*.mobileprovision

# VoltMX generated files
syncclientcode.zip
HeadlessBuild.properties
*.diff
*.patch

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# IDE and editor files
.vscode/launch.json
.vscode/tasks.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# VoltMX specific temp files
*.tmp
*.bak
*.backup

# Android specific
local.properties
*.jks

# iOS specific
*.xcuserstate
*.xcworkspace/xcuserdata/
DerivedData/

# Windows specific
*.exe
*.msi

# Test files
test-results/
coverage/
