<Tbl><Array><Nil/></Array><Hash><HashCell><Str value="CaseManagementData"/><Str value="CaseManagementData"/></HashCell><HashCell><Str value="ERR_SP_00"/><Str value="ERR_SP_00"/></HashCell><HashCell><Str value="ERR_SP_01"/><Str value="ERR_SP_01"/></HashCell><HashCell><Str value="ERR_SP_02"/><Str value="ERR_SP_02"/></HashCell><HashCell><Str value="ERR_SP_03"/><Str value="ERR_SP_03"/></HashCell><HashCell><Str value="ERR_SP_04"/><Str value="ERR_SP_04"/></HashCell><HashCell><Str value="ERR_SP_05"/><Str value="ERR_SP_05"/></HashCell><HashCell><Str value="ERR_SP_06"/><Str value="ERR_SP_06"/></HashCell><HashCell><Str value="GetCarInfo"/><Str value="GetCarInfo"/></HashCell><HashCell><Str value="GetParkings"/><Str value="GetParkings"/></HashCell><HashCell><Str value="GetSignals"/><Str value="GetSignals"/></HashCell><HashCell><Str value="Instance"/><Str value="Instance"/></HashCell><HashCell><Str value="LabelAanbrengen"/><Str value="LabelAanbrengen"/></HashCell><HashCell><Str value="Location"/><Str value="Location"/></HashCell><HashCell><Str value="LocationData"/><Str value="LocationData"/></HashCell><HashCell><Str value="NotificatieAanbrengen"/><Str value="NotificatieAanbrengen"/></HashCell><HashCell><Str value="Offence"/><Str value="Offence"/></HashCell><HashCell><Str value="Person"/><Str value="Person"/></HashCell><HashCell><Str value="Planning"/><Str value="Planning"/></HashCell><HashCell><Str value="RED-ASGN-01"/><Str value="RED-ASGN-01"/></HashCell><HashCell><Str value="RED-ASGN-02"/><Str value="RED-ASGN-02"/></HashCell><HashCell><Str value="RED-ASGN-03"/><Str value="RED-ASGN-03"/></HashCell><HashCell><Str value="RED-ASGN-04"/><Str value="RED-ASGN-04"/></HashCell><HashCell><Str value="RED-ASGN-05"/><Str value="RED-ASGN-05"/></HashCell><HashCell><Str value="RED-ASGN-06"/><Str value="RED-ASGN-06"/></HashCell><HashCell><Str value="RED-ASGN-07"/><Str value="RED-ASGN-07"/></HashCell><HashCell><Str value="RED-ASGN-08"/><Str value="RED-ASGN-08"/></HashCell><HashCell><Str value="RED-ASGN-09"/><Str value="RED-ASGN-09"/></HashCell><HashCell><Str value="RED-ASGN-10"/><Str value="RED-ASGN-10"/></HashCell><HashCell><Str value="RED-ASGN-11"/><Str value="RED-ASGN-11"/></HashCell><HashCell><Str value="RED-ASGN-12"/><Str value="RED-ASGN-12"/></HashCell><HashCell><Str value="ReferenceData"/><Str value="ReferenceData"/></HashCell><HashCell><Str value="Users"/><Str value="Users"/></HashCell><HashCell><Str value="VEHICLE_SIGNAL"/><Str value="VEHICLE_SIGNAL"/></HashCell><HashCell><Str value="Vehicle"/><Str value="Vehicle"/></HashCell><HashCell><Str value="WachtenOpVerwijderen"/><Str value="WachtenOpVerwijderen"/></HashCell><HashCell><Str value="WaitingForLocation"/><Str value="WaitingForLocation"/></HashCell><HashCell><Str value="_usesLegalAssistance"/><Str value="_usesLegalAssistance"/></HashCell><HashCell><Str value="appmode_checklabel"/><Str value="appmode_checklabel"/></HashCell><HashCell><Str value="appmode_labeloverview"/><Str value="appmode_labeloverview"/></HashCell><HashCell><Str value="appmode_register"/><Str value="appmode_register"/></HashCell><HashCell><Str value="appmode_registerconcept"/><Str value="appmode_registerconcept"/></HashCell><HashCell><Str value="appmode_registerlabel"/><Str value="appmode_registerlabel"/></HashCell><HashCell><Str value="bt_add"/><Str value="bt_add"/></HashCell><HashCell><Str value="bt_again"/><Str value="bt_again"/></HashCell><HashCell><Str value="bt_back"/><Str value="bt_back"/></HashCell><HashCell><Str value="bt_cancel"/><Str value="bt_cancel"/></HashCell><HashCell><Str value="bt_check"/><Str value="bt_check"/></HashCell><HashCell><Str value="bt_complete"/><Str value="bt_complete"/></HashCell><HashCell><Str value="bt_continue"/><Str value="bt_continue"/></HashCell><HashCell><Str value="bt_deselectAll"/><Str value="bt_deselectAll"/></HashCell><HashCell><Str value="bt_directPV"/><Str value="bt_directPV"/></HashCell><HashCell><Str value="bt_done"/><Str value="bt_done"/></HashCell><HashCell><Str value="bt_edit"/><Str value="bt_edit"/></HashCell><HashCell><Str value="bt_exit"/><Str value="bt_exit"/></HashCell><HashCell><Str value="bt_info"/><Str value="bt_info"/></HashCell><HashCell><Str value="bt_lightoff"/><Str value="bt_lightoff"/></HashCell><HashCell><Str value="bt_lightoff_once"/><Str value="bt_lightoff_once"/></HashCell><HashCell><Str value="bt_lighton"/><Str value="bt_lighton"/></HashCell><HashCell><Str value="bt_lighton_once"/><Str value="bt_lighton_once"/></HashCell><HashCell><Str value="bt_map"/><Str value="bt_map"/></HashCell><HashCell><Str value="bt_markOverviewPhoto"/><Str value="bt_markOverviewPhoto"/></HashCell><HashCell><Str value="bt_new"/><Str value="bt_new"/></HashCell><HashCell><Str value="bt_no"/><Str value="bt_no"/></HashCell><HashCell><Str value="bt_ok"/><Str value="bt_ok"/></HashCell><HashCell><Str value="bt_register"/><Str value="bt_register"/></HashCell><HashCell><Str value="bt_remove"/><Str value="bt_remove"/></HashCell><HashCell><Str value="bt_retry"/><Str value="bt_retry"/></HashCell><HashCell><Str value="bt_selectAll"/><Str value="bt_selectAll"/></HashCell><HashCell><Str value="bt_send"/><Str value="bt_send"/></HashCell><HashCell><Str value="bt_stop"/><Str value="bt_stop"/></HashCell><HashCell><Str value="bt_unknown"/><Str value="bt_unknown"/></HashCell><HashCell><Str value="bt_vehicle_scan"/><Str value="bt_vehicle_scan"/></HashCell><HashCell><Str value="bt_view"/><Str value="bt_view"/></HashCell><HashCell><Str value="bt_yes"/><Str value="bt_yes"/></HashCell><HashCell><Str value="camera_permission_dialog_message"/><Str value="To enable photo capture, this app requires access to your camera. Please grant permission by opening the Settings app."/></HashCell><HashCell><Str value="camera_permission_dialog_title"/><Str value="Allow Twyns App to Access Your Camera"/></HashCell><HashCell><Str value="camera_permission_scan_dialog_message"/><Str value="To enable document scanning, this app requires access to your camera. Please grant permission by opening the Settings app."/></HashCell><HashCell><Str value="cancel"/><Str value="Cancel"/></HashCell><HashCell><Str value="drivingCard"/><Str value="Driving licence"/></HashCell><HashCell><Str value="e_area0001"/><Str value="e_area0001"/></HashCell><HashCell><Str value="e_area0002"/><Str value="e_area0002"/></HashCell><HashCell><Str value="e_log0001"/><Str value="e_log0001"/></HashCell><HashCell><Str value="e_log0002"/><Str value="e_log0002"/></HashCell><HashCell><Str value="e_log0003"/><Str value="e_log0003"/></HashCell><HashCell><Str value="e_log0004"/><Str value="e_log0004"/></HashCell><HashCell><Str value="e_mand0002"/><Str value="e_mand0002"/></HashCell><HashCell><Str value="e_mandByLawNotFound"/><Str value="e_mandByLawNotFound"/></HashCell><HashCell><Str value="e_multipleByLaws"/><Str value="e_multipleByLaws"/></HashCell><HashCell><Str value="e_noInstanceparameter"/><Str value="e_noInstanceparameter"/></HashCell><HashCell><Str value="e_prt0001"/><Str value="e_prt0001"/></HashCell><HashCell><Str value="e_select_zone"/><Str value="e_select_zone"/></HashCell><HashCell><Str value="e_ser0001"/><Str value="e_ser0001"/></HashCell><HashCell><Str value="e_ser0002"/><Str value="e_ser0002"/></HashCell><HashCell><Str value="e_ser0003"/><Str value="e_ser0003"/></HashCell><HashCell><Str value="e_serverError"/><Str value="e_serverError"/></HashCell><HashCell><Str value="e_serverErrorNPR_1"/><Str value="e_serverErrorNPR_1"/></HashCell><HashCell><Str value="e_serverErrorNPR_100"/><Str value="e_serverErrorNPR_100"/></HashCell><HashCell><Str value="e_serverErrorNPR_104"/><Str value="e_serverErrorNPR_104"/></HashCell><HashCell><Str value="e_serverErrorNPR_302"/><Str value="e_serverErrorNPR_302"/></HashCell><HashCell><Str value="e_serverErrorNPR_5"/><Str value="e_serverErrorNPR_5"/></HashCell><HashCell><Str value="e_serverErrorNPR_999"/><Str value="e_serverErrorNPR_999"/></HashCell><HashCell><Str value="e_sync0001"/><Str value="e_sync0001"/></HashCell><HashCell><Str value="e_sync0002"/><Str value="e_sync0002"/></HashCell><HashCell><Str value="e_sync0003"/><Str value="e_sync0003"/></HashCell><HashCell><Str value="e_upgradeError"/><Str value="e_upgradeError"/></HashCell><HashCell><Str value="e_userNotFound"/><Str value="e_userNotFound"/></HashCell><HashCell><Str value="e_val00004"/><Str value="e_val00004"/></HashCell><HashCell><Str value="e_val00005"/><Str value="e_val00005"/></HashCell><HashCell><Str value="e_val00006"/><Str value="e_val00006"/></HashCell><HashCell><Str value="e_val00007"/><Str value="e_val00007"/></HashCell><HashCell><Str value="e_val00008"/><Str value="e_val00008"/></HashCell><HashCell><Str value="e_val00009"/><Str value="e_val00009"/></HashCell><HashCell><Str value="e_val00010"/><Str value="e_val00010"/></HashCell><HashCell><Str value="hdr_default"/><Str value="hdr_default"/></HashCell><HashCell><Str value="hdr_manual"/><Str value="hdr_manual"/></HashCell><HashCell><Str value="hdr_rdw"/><Str value="hdr_rdw"/></HashCell><HashCell><Str value="hdr_scan"/><Str value="hdr_scan"/></HashCell><HashCell><Str value="hdr_scanRdw"/><Str value="hdr_scanRdw"/></HashCell><HashCell><Str value="i_anpr0001"/><Str value="i_anpr0001"/></HashCell><HashCell><Str value="i_anpr0002"/><Str value="i_anpr0002"/></HashCell><HashCell><Str value="i_confirmsync"/><Str value="i_confirmsync"/></HashCell><HashCell><Str value="i_connectPrinterFailed"/><Str value="i_connectPrinterFailed"/></HashCell><HashCell><Str value="i_daysago"/><Str value="i_daysago"/></HashCell><HashCell><Str value="i_deceased"/><Str value="i_deceased"/></HashCell><HashCell><Str value="i_declinedLegalAssistance"/><Str value="i_declinedLegalAssistance"/></HashCell><HashCell><Str value="i_emigrate"/><Str value="i_emigrate"/></HashCell><HashCell><Str value="i_faulted"/><Str value="i_faulted"/></HashCell><HashCell><Str value="i_fsc0001"/><Str value="i_fsc0001"/></HashCell><HashCell><Str value="i_fsc0002"/><Str value="i_fsc0002"/></HashCell><HashCell><Str value="i_fsc0007"/><Str value="i_fsc0007"/></HashCell><HashCell><Str value="i_hoursago"/><Str value="i_hoursago"/></HashCell><HashCell><Str value="i_interpretationCommunicated"/><Str value="i_interpretationCommunicated"/></HashCell><HashCell><Str value="i_interpretationNotCommunicated"/><Str value="i_interpretationNotCommunicated"/></HashCell><HashCell><Str value="i_interpreter"/><Str value="i_interpreter"/></HashCell><HashCell><Str value="i_legalAssistanceCommunicated"/><Str value="i_legalAssistanceCommunicated"/></HashCell><HashCell><Str value="i_legalAssistanceNotCommunicated"/><Str value="i_legalAssistanceNotCommunicated"/></HashCell><HashCell><Str value="i_noCities"/><Str value="i_noCities"/></HashCell><HashCell><Str value="i_noMunicipalities"/><Str value="i_noMunicipalities"/></HashCell><HashCell><Str value="i_no_person"/><Str value="i_no_person"/></HashCell><HashCell><Str value="i_notDeclinedLegalAssistance"/><Str value="i_notDeclinedLegalAssistance"/></HashCell><HashCell><Str value="i_notUsesThisRight"/><Str value="i_notUsesThisRight"/></HashCell><HashCell><Str value="i_offenceCommunicated"/><Str value="i_offenceCommunicated"/></HashCell><HashCell><Str value="i_offenceNotCommunicated"/><Str value="i_offenceNotCommunicated"/></HashCell><HashCell><Str value="i_personNotFound"/><Str value="i_personNotFound"/></HashCell><HashCell><Str value="i_restart"/><Str value="i_restart"/></HashCell><HashCell><Str value="i_select_street"/><Str value="i_select_street"/></HashCell><HashCell><Str value="i_syncwarning"/><Str value="i_syncwarning"/></HashCell><HashCell><Str value="i_tooManyPersonRecords"/><Str value="i_tooManyPersonRecords"/></HashCell><HashCell><Str value="i_translationLanguage"/><Str value="i_translationLanguage"/></HashCell><HashCell><Str value="i_usesThisRight"/><Str value="i_usesThisRight"/></HashCell><HashCell><Str value="idCard"/><Str value="Identity Card"/></HashCell><HashCell><Str value="l_2Characterinput"/><Str value="l_2Characterinput"/></HashCell><HashCell><Str value="l_3Characterinput"/><Str value="l_3Characterinput"/></HashCell><HashCell><Str value="l_Diagnostics"/><Str value="l_Diagnostics"/></HashCell><HashCell><Str value="l_HouseNumberMustBeNumeric"/><Str value="l_HouseNumberMustBeNumeric"/></HashCell><HashCell><Str value="l_NSproduction"/><Str value="l_NSproduction"/></HashCell><HashCell><Str value="l_NSproductionEducation"/><Str value="l_NSproductionEducation"/></HashCell><HashCell><Str value="l_NoPlate"/><Str value="l_NoPlate"/></HashCell><HashCell><Str value="l_NoPlates"/><Str value="l_NoPlates"/></HashCell><HashCell><Str value="l_NoPlatesConfirmation"/><Str value="l_NoPlatesConfirmation"/></HashCell><HashCell><Str value="l_NoRefreshPlate"/><Str value="l_NoRefreshPlate"/></HashCell><HashCell><Str value="l_OVdemo"/><Str value="l_OVdemo"/></HashCell><HashCell><Str value="l_OffenceCommunicatedNoStatement"/><Str value="l_OffenceCommunicatedNoStatement"/></HashCell><HashCell><Str value="l_Pin"/><Str value="l_Pin"/></HashCell><HashCell><Str value="l_RDWowner"/><Str value="l_RDWowner"/></HashCell><HashCell><Str value="l_RDWownerCheck"/><Str value="l_RDWownerCheck"/></HashCell><HashCell><Str value="l_StatementPledge"/><Str value="l_StatementPledge"/></HashCell><HashCell><Str value="l_WatchGPSInterval"/><Str value="l_WatchGPSInterval"/></HashCell><HashCell><Str value="l_about"/><Str value="l_about"/></HashCell><HashCell><Str value="l_acceptance"/><Str value="l_acceptance"/></HashCell><HashCell><Str value="l_acceptanceTest"/><Str value="l_acceptanceTest"/></HashCell><HashCell><Str value="l_acceptance_pt"/><Str value="l_acceptance_pt"/></HashCell><HashCell><Str value="l_activeCases"/><Str value="l_activeCases"/></HashCell><HashCell><Str value="l_addAddressWithoutGPS"/><Str value="l_addAddressWithoutGPS"/></HashCell><HashCell><Str value="l_addData"/><Str value="l_addData"/></HashCell><HashCell><Str value="l_addLabel"/><Str value="l_addLabel"/></HashCell><HashCell><Str value="l_addTax"/><Str value="l_addTax"/></HashCell><HashCell><Str value="l_addTaxDisplay"/><Str value="l_addTaxDisplay"/></HashCell><HashCell><Str value="l_additionalAssesment"/><Str value="l_additionalAssesment"/></HashCell><HashCell><Str value="l_address"/><Str value="l_address"/></HashCell><HashCell><Str value="l_addressUnderInvestigation"/><Str value="l_addressUnderInvestigation"/></HashCell><HashCell><Str value="l_administrationcosts"/><Str value="l_administrationcosts"/></HashCell><HashCell><Str value="l_all"/><Str value="l_all"/></HashCell><HashCell><Str value="l_allowed"/><Str value="l_allowed"/></HashCell><HashCell><Str value="l_amountDue"/><Str value="l_amountDue"/></HashCell><HashCell><Str value="l_amountSanction"/><Str value="l_amountSanction"/></HashCell><HashCell><Str value="l_applyLabel"/><Str value="l_applyLabel"/></HashCell><HashCell><Str value="l_applyfilter"/><Str value="l_applyfilter"/></HashCell><HashCell><Str value="l_area"/><Str value="l_area"/></HashCell><HashCell><Str value="l_areas"/><Str value="l_areas"/></HashCell><HashCell><Str value="l_askAdministrator"/><Str value="l_askAdministrator"/></HashCell><HashCell><Str value="l_assessmentNumber"/><Str value="l_assessmentNumber"/></HashCell><HashCell><Str value="l_assessor"/><Str value="l_assessor"/></HashCell><HashCell><Str value="l_assignedArea"/><Str value="l_assignedArea"/></HashCell><HashCell><Str value="l_attempt"/><Str value="l_attempt"/></HashCell><HashCell><Str value="l_authenticating_user"/><Str value="l_authenticating_user"/></HashCell><HashCell><Str value="l_auto_restart_enabled"/><Str value="l_auto_restart_enabled"/></HashCell><HashCell><Str value="l_auto_scan_time"/><Str value="l_auto_scan_time"/></HashCell><HashCell><Str value="l_averages"/><Str value="l_averages"/></HashCell><HashCell><Str value="l_batches"/><Str value="l_batches"/></HashCell><HashCell><Str value="l_birthdate"/><Str value="l_birthdate"/></HashCell><HashCell><Str value="l_branchNumber"/><Str value="l_branchNumber"/></HashCell><HashCell><Str value="l_brand"/><Str value="l_brand"/></HashCell><HashCell><Str value="l_brandtype"/><Str value="l_brandtype"/></HashCell><HashCell><Str value="l_building"/><Str value="l_building"/></HashCell><HashCell><Str value="l_building100chars"/><Str value="l_building100chars"/></HashCell><HashCell><Str value="l_bus"/><Str value="l_bus"/></HashCell><HashCell><Str value="l_by"/><Str value="l_by"/></HashCell><HashCell><Str value="l_calculatedPrice"/><Str value="l_calculatedPrice"/></HashCell><HashCell><Str value="l_cancel"/><Str value="l_cancel"/></HashCell><HashCell><Str value="l_cannotRegisterYourself"/><Str value="l_cannotRegisterYourself"/></HashCell><HashCell><Str value="l_cannotUseAddress"/><Str value="l_cannotUseAddress"/></HashCell><HashCell><Str value="l_car"/><Str value="l_car"/></HashCell><HashCell><Str value="l_cardNumber"/><Str value="l_cardNumber"/></HashCell><HashCell><Str value="l_cardcheck"/><Str value="l_cardcheck"/></HashCell><HashCell><Str value="l_cards"/><Str value="l_cards"/></HashCell><HashCell><Str value="l_case"/><Str value="l_case"/></HashCell><HashCell><Str value="l_caseAndTaskTypeNotFound"/><Str value="l_caseAndTaskTypeNotFound"/></HashCell><HashCell><Str value="l_caseNoLongerOnServer"/><Str value="l_caseNoLongerOnServer"/></HashCell><HashCell><Str value="l_caseNotFound"/><Str value="l_caseNotFound"/></HashCell><HashCell><Str value="l_caseNotRetrieved"/><Str value="l_caseNotRetrieved"/></HashCell><HashCell><Str value="l_caseOnlineFailedOutboxQuestion"/><Str value="l_caseOnlineFailedOutboxQuestion"/></HashCell><HashCell><Str value="l_caseOnlineFailedSetToOutbox"/><Str value="l_caseOnlineFailedSetToOutbox"/></HashCell><HashCell><Str value="l_caseType"/><Str value="l_caseType"/></HashCell><HashCell><Str value="l_caseUpdateFailed"/><Str value="l_caseUpdateFailed"/></HashCell><HashCell><Str value="l_caseWillBeClosed"/><Str value="l_caseWillBeClosed"/></HashCell><HashCell><Str value="l_case_not_opened"/><Str value="l_case_not_opened"/></HashCell><HashCell><Str value="l_cases"/><Str value="l_cases"/></HashCell><HashCell><Str value="l_casesToBeUpdated"/><Str value="l_casesToBeUpdated"/></HashCell><HashCell><Str value="l_cashAmount"/><Str value="l_cashAmount"/></HashCell><HashCell><Str value="l_changeAddress"/><Str value="l_changeAddress"/></HashCell><HashCell><Str value="l_changeCaseTo"/><Str value="l_changeCaseTo"/></HashCell><HashCell><Str value="l_changeEnvironment"/><Str value="l_changeEnvironment"/></HashCell><HashCell><Str value="l_changeFilter"/><Str value="l_changeFilter"/></HashCell><HashCell><Str value="l_changeHouseNumber"/><Str value="l_changeHouseNumber"/></HashCell><HashCell><Str value="l_changeInstance"/><Str value="l_changeInstance"/></HashCell><HashCell><Str value="l_changePosition"/><Str value="l_changePosition"/></HashCell><HashCell><Str value="l_changeTheme"/><Str value="l_changeTheme"/></HashCell><HashCell><Str value="l_changeTravelMode"/><Str value="l_changeTravelMode"/></HashCell><HashCell><Str value="l_channel"/><Str value="l_channel"/></HashCell><HashCell><Str value="l_check"/><Str value="l_check"/></HashCell><HashCell><Str value="l_checkBounds"/><Str value="l_checkBounds"/></HashCell><HashCell><Str value="l_checkBoundsValue"/><Str value="l_checkBoundsValue"/></HashCell><HashCell><Str value="l_checkLicensePlate"/><Str value="l_checkLicensePlate"/></HashCell><HashCell><Str value="l_checkPin"/><Str value="l_checkPin"/></HashCell><HashCell><Str value="l_checkTime"/><Str value="l_checkTime"/></HashCell><HashCell><Str value="l_checkUserDatabase"/><Str value="l_checkUserDatabase"/></HashCell><HashCell><Str value="l_checkUserPV"/><Str value="l_checkUserPV"/></HashCell><HashCell><Str value="l_check_types"/><Str value="l_check_types"/></HashCell><HashCell><Str value="l_checkcard"/><Str value="l_checkcard"/></HashCell><HashCell><Str value="l_checked"/><Str value="l_checked"/></HashCell><HashCell><Str value="l_checkedOut"/><Str value="l_checkedOut"/></HashCell><HashCell><Str value="l_checkedOutFollow"/><Str value="l_checkedOutFollow"/></HashCell><HashCell><Str value="l_checking_service"/><Str value="l_checking_service"/></HashCell><HashCell><Str value="l_checklocation"/><Str value="l_checklocation"/></HashCell><HashCell><Str value="l_checkout"/><Str value="l_checkout"/></HashCell><HashCell><Str value="l_checks"/><Str value="l_checks"/></HashCell><HashCell><Str value="l_checkvehicle"/><Str value="l_checkvehicle"/></HashCell><HashCell><Str value="l_choose"/><Str value="l_choose"/></HashCell><HashCell><Str value="l_chooseCity"/><Str value="l_chooseCity"/></HashCell><HashCell><Str value="l_chooseHost"/><Str value="l_chooseHost"/></HashCell><HashCell><Str value="l_chooseMunicipality"/><Str value="l_chooseMunicipality"/></HashCell><HashCell><Str value="l_chooseOneCheckType"/><Str value="l_chooseOneCheckType"/></HashCell><HashCell><Str value="l_chooseTaskOutcome"/><Str value="l_chooseTaskOutcome"/></HashCell><HashCell><Str value="l_chooseTeam"/><Str value="l_chooseTeam"/></HashCell><HashCell><Str value="l_chooseZone"/><Str value="l_chooseZone"/></HashCell><HashCell><Str value="l_choosearea"/><Str value="l_choosearea"/></HashCell><HashCell><Str value="l_chooselabel"/><Str value="l_chooselabel"/></HashCell><HashCell><Str value="l_choosestreet"/><Str value="l_choosestreet"/></HashCell><HashCell><Str value="l_choosetask"/><Str value="l_choosetask"/></HashCell><HashCell><Str value="l_choosevalue"/><Str value="l_choosevalue"/></HashCell><HashCell><Str value="l_chosenZone"/><Str value="l_chosenZone"/></HashCell><HashCell><Str value="l_chosenaddress"/><Str value="l_chosenaddress"/></HashCell><HashCell><Str value="l_city"/><Str value="l_city"/></HashCell><HashCell><Str value="l_city36chars"/><Str value="l_city36chars"/></HashCell><HashCell><Str value="l_cityNotOffenceCity"/><Str value="l_cityNotOffenceCity"/></HashCell><HashCell><Str value="l_claimFailed"/><Str value="l_claimFailed"/></HashCell><HashCell><Str value="l_claimedByMe"/><Str value="l_claimedByMe"/></HashCell><HashCell><Str value="l_claimedByOther"/><Str value="l_claimedByOther"/></HashCell><HashCell><Str value="l_claimedByOtherRetrievingCases"/><Str value="l_claimedByOtherRetrievingCases"/></HashCell><HashCell><Str value="l_clamp"/><Str value="l_clamp"/></HashCell><HashCell><Str value="l_clampNumber"/><Str value="l_clampNumber"/></HashCell><HashCell><Str value="l_clampNumberNotFound"/><Str value="l_clampNumberNotFound"/></HashCell><HashCell><Str value="l_clampTime"/><Str value="l_clampTime"/></HashCell><HashCell><Str value="l_clickToAddPhotos"/><Str value="l_clickToAddPhotos"/></HashCell><HashCell><Str value="l_close"/><Str value="l_close"/></HashCell><HashCell><Str value="l_closePDF"/><Str value="l_closePDF"/></HashCell><HashCell><Str value="l_cocnumber"/><Str value="l_cocnumber"/></HashCell><HashCell><Str value="l_code"/><Str value="l_code"/></HashCell><HashCell><Str value="l_color"/><Str value="l_color"/></HashCell><HashCell><Str value="l_concepts"/><Str value="l_concepts"/></HashCell><HashCell><Str value="l_confirm_removelist"/><Str value="l_confirm_removelist"/></HashCell><HashCell><Str value="l_connectiondescription"/><Str value="l_connectiondescription"/></HashCell><HashCell><Str value="l_continueOffline"/><Str value="l_continueOffline"/></HashCell><HashCell><Str value="l_continueWithoutConnection"/><Str value="l_continueWithoutConnection"/></HashCell><HashCell><Str value="l_coordinates"/><Str value="l_coordinates"/></HashCell><HashCell><Str value="l_countries"/><Str value="l_countries"/></HashCell><HashCell><Str value="l_country"/><Str value="l_country"/></HashCell><HashCell><Str value="l_countryPlateUnknownChange"/><Str value="l_countryPlateUnknownChange"/></HashCell><HashCell><Str value="l_countryUnknown"/><Str value="l_countryUnknown"/></HashCell><HashCell><Str value="l_countryUnknownCode"/><Str value="l_countryUnknownCode"/></HashCell><HashCell><Str value="l_countryadress"/><Str value="l_countryadress"/></HashCell><HashCell><Str value="l_countryoforigin"/><Str value="l_countryoforigin"/></HashCell><HashCell><Str value="l_createPin"/><Str value="l_createPin"/></HashCell><HashCell><Str value="l_currentLocation"/><Str value="l_currentLocation"/></HashCell><HashCell><Str value="l_currentPosition"/><Str value="l_currentPosition"/></HashCell><HashCell><Str value="l_currentValidity"/><Str value="l_currentValidity"/></HashCell><HashCell><Str value="l_current_task"/><Str value="l_current_task"/></HashCell><HashCell><Str value="l_dashesOnly"/><Str value="l_dashesOnly"/></HashCell><HashCell><Str value="l_date"/><Str value="l_date"/></HashCell><HashCell><Str value="l_dateTimeFuture"/><Str value="l_dateTimeFuture"/></HashCell><HashCell><Str value="l_dateTimePast"/><Str value="l_dateTimePast"/></HashCell><HashCell><Str value="l_datetime"/><Str value="l_datetime"/></HashCell><HashCell><Str value="l_daysMonth"/><Str value="l_daysMonth"/></HashCell><HashCell><Str value="l_declinedLegalAssistance"/><Str value="l_declinedLegalAssistance"/></HashCell><HashCell><Str value="l_description"/><Str value="l_description"/></HashCell><HashCell><Str value="l_details"/><Str value="l_details"/></HashCell><HashCell><Str value="l_development"/><Str value="l_development"/></HashCell><HashCell><Str value="l_digit_count"/><Str value="l_digit_count"/></HashCell><HashCell><Str value="l_directPerfectview"/><Str value="l_directPerfectview"/></HashCell><HashCell><Str value="l_disabled_parking"/><Str value="l_disabled_parking"/></HashCell><HashCell><Str value="l_distance"/><Str value="l_distance"/></HashCell><HashCell><Str value="l_doNotChangePlate"/><Str value="l_doNotChangePlate"/></HashCell><HashCell><Str value="l_doNotHaveFingerprints"/><Str value="l_doNotHaveFingerprints"/></HashCell><HashCell><Str value="l_doNotHaveLogOnCode"/><Str value="l_doNotHaveLogOnCode"/></HashCell><HashCell><Str value="l_doYouWantToPasteData"/><Str value="l_doYouWantToPasteData"/></HashCell><HashCell><Str value="l_documentAlreadyProcessedException"/><Str value="l_documentAlreadyProcessedException"/></HashCell><HashCell><Str value="l_documentNotFoundException"/><Str value="l_documentNotFoundException"/></HashCell><HashCell><Str value="l_duplicate"/><Str value="l_duplicate"/></HashCell><HashCell><Str value="l_duplicateScan"/><Str value="l_duplicateScan"/></HashCell><HashCell><Str value="l_dutch"/><Str value="l_dutch"/></HashCell><HashCell><Str value="l_editAddress"/><Str value="l_editAddress"/></HashCell><HashCell><Str value="l_editData"/><Str value="l_editData"/></HashCell><HashCell><Str value="l_editExistingOptionsVariables"/><Str value="l_editExistingOptionsVariables"/></HashCell><HashCell><Str value="l_editIdentification"/><Str value="l_editIdentification"/></HashCell><HashCell><Str value="l_editKindOfViolation"/><Str value="l_editKindOfViolation"/></HashCell><HashCell><Str value="l_editLicensePlate"/><Str value="l_editLicensePlate"/></HashCell><HashCell><Str value="l_editTime"/><Str value="l_editTime"/></HashCell><HashCell><Str value="l_email"/><Str value="l_email"/></HashCell><HashCell><Str value="l_emmDataInCorrect"/><Str value="l_emmDataInCorrect"/></HashCell><HashCell><Str value="l_emmNotCorrect"/><Str value="l_emmNotCorrect"/></HashCell><HashCell><Str value="l_employeeNumberPrint"/><Str value="l_employeeNumberPrint"/></HashCell><HashCell><Str value="l_emptyPrinterMac"/><Str value="l_emptyPrinterMac"/></HashCell><HashCell><Str value="l_emptyPrinterName"/><Str value="l_emptyPrinterName"/></HashCell><HashCell><Str value="l_end"/><Str value="l_end"/></HashCell><HashCell><Str value="l_endOfList"/><Str value="l_endOfList"/></HashCell><HashCell><Str value="l_endTime"/><Str value="l_endTime"/></HashCell><HashCell><Str value="l_enforcementObject"/><Str value="l_enforcementObject"/></HashCell><HashCell><Str value="l_english"/><Str value="l_english"/></HashCell><HashCell><Str value="l_enter5DigitPin"/><Str value="l_enter5DigitPin"/></HashCell><HashCell><Str value="l_enterNumber"/><Str value="l_enterNumber"/></HashCell><HashCell><Str value="l_enterPin"/><Str value="l_enterPin"/></HashCell><HashCell><Str value="l_enterUsernameFirst"/><Str value="l_enterUsernameFirst"/></HashCell><HashCell><Str value="l_enteraddress"/><Str value="l_enteraddress"/></HashCell><HashCell><Str value="l_environment"/><Str value="l_environment"/></HashCell><HashCell><Str value="l_environmentListActivated"/><Str value="l_environmentListActivated"/></HashCell><HashCell><Str value="l_environmentalClassification"/><Str value="l_environmentalClassification"/></HashCell><HashCell><Str value="l_environmentalClassificationPrefix"/><Str value="l_environmentalClassificationPrefix"/></HashCell><HashCell><Str value="l_error"/><Str value="l_error"/></HashCell><HashCell><Str value="l_errorCode"/><Str value="l_errorCode"/></HashCell><HashCell><Str value="l_errorDuringUpgrade"/><Str value="l_errorDuringUpgrade"/></HashCell><HashCell><Str value="l_errorUserCheck"/><Str value="l_errorUserCheck"/></HashCell><HashCell><Str value="l_errored"/><Str value="l_errored"/></HashCell><HashCell><Str value="l_europeanVehicleType"/><Str value="l_europeanVehicleType"/></HashCell><HashCell><Str value="l_execByPartner"/><Str value="l_execByPartner"/></HashCell><HashCell><Str value="l_executing_anpr"/><Str value="l_executing_anpr"/></HashCell><HashCell><Str value="l_exemption"/><Str value="l_exemption"/></HashCell><HashCell><Str value="l_exemptionstartdate"/><Str value="l_exemptionstartdate"/></HashCell><HashCell><Str value="l_existingOptionsVariables"/><Str value="l_existingOptionsVariables"/></HashCell><HashCell><Str value="l_exit"/><Str value="l_exit"/></HashCell><HashCell><Str value="l_exitApplication"/><Str value="l_exitApplication"/></HashCell><HashCell><Str value="l_exp_date"/><Str value="l_exp_date"/></HashCell><HashCell><Str value="l_explanation"/><Str value="l_explanation"/></HashCell><HashCell><Str value="l_externalcollector"/><Str value="l_externalcollector"/></HashCell><HashCell><Str value="l_extraInformation"/><Str value="l_extraInformation"/></HashCell><HashCell><Str value="l_favorites"/><Str value="l_favorites"/></HashCell><HashCell><Str value="l_fillStreetCity"/><Str value="l_fillStreetCity"/></HashCell><HashCell><Str value="l_filter"/><Str value="l_filter"/></HashCell><HashCell><Str value="l_fineWithdrawn"/><Str value="l_fineWithdrawn"/></HashCell><HashCell><Str value="l_finished"/><Str value="l_finished"/></HashCell><HashCell><Str value="l_firstName"/><Str value="l_firstname"/></HashCell><HashCell><Str value="l_flash_auto"/><Str value="l_flash_auto"/></HashCell><HashCell><Str value="l_flash_off"/><Str value="l_flash_off"/></HashCell><HashCell><Str value="l_flash_on"/><Str value="l_flash_on"/></HashCell><HashCell><Str value="l_folderNotFound"/><Str value="l_folderNotFound"/></HashCell><HashCell><Str value="l_follow"/><Str value="l_follow"/></HashCell><HashCell><Str value="l_followUp"/><Str value="l_followUp"/></HashCell><HashCell><Str value="l_found"/><Str value="l_found"/></HashCell><HashCell><Str value="l_foundHistoricCase"/><Str value="l_foundHistoricCase"/></HashCell><HashCell><Str value="l_fuel"/><Str value="l_fuel"/></HashCell><HashCell><Str value="l_fullName"/><Str value="l_fullName"/></HashCell><HashCell><Str value="l_fullname"/><Str value="l_fullname"/></HashCell><HashCell><Str value="l_gender"/><Str value="l_gender"/></HashCell><HashCell><Str value="l_georgia"/><Str value="l_georgia"/></HashCell><HashCell><Str value="l_germanPlateDash"/><Str value="l_germanPlateDash"/></HashCell><HashCell><Str value="l_germany"/><Str value="l_germany"/></HashCell><HashCell><Str value="l_getDataFailed"/><Str value="l_getDataFailed"/></HashCell><HashCell><Str value="l_getParkRegistrations"/><Str value="l_getParkRegistrations"/></HashCell><HashCell><Str value="l_getPreviousFines"/><Str value="l_getPreviousFines"/></HashCell><HashCell><Str value="l_getZonesTerminals"/><Str value="l_getZonesTerminals"/></HashCell><HashCell><Str value="l_give5NumberPin"/><Str value="l_give5NumberPin"/></HashCell><HashCell><Str value="l_giveValidParkingRight"/><Str value="l_giveValidParkingRight"/></HashCell><HashCell><Str value="l_gps_no_fix"/><Str value="l_gps_no_fix"/></HashCell><HashCell><Str value="l_gps_refresh"/><Str value="l_gps_refresh"/></HashCell><HashCell><Str value="l_handle"/><Str value="l_handle"/></HashCell><HashCell><Str value="l_handling"/><Str value="l_handling"/></HashCell><HashCell><Str value="l_hasParkingRight"/><Str value="l_hasParkingRight"/></HashCell><HashCell><Str value="l_hasValidParkingRight"/><Str value="l_hasValidParkingRight"/></HashCell><HashCell><Str value="l_haveToUpdate"/><Str value="l_haveToUpdate"/></HashCell><HashCell><Str value="l_haveToUpdateMDM"/><Str value="l_haveToUpdateMDM"/></HashCell><HashCell><Str value="l_haveToUpdateStoreMDM"/><Str value="l_haveToUpdateStoreMDM"/></HashCell><HashCell><Str value="l_hectometerMarkersNearby"/><Str value="l_hectometerMarkersNearby"/></HashCell><HashCell><Str value="l_history"/><Str value="l_history"/></HashCell><HashCell><Str value="l_hostlocation"/><Str value="l_hostlocation"/></HashCell><HashCell><Str value="l_hours"/><Str value="l_hours"/></HashCell><HashCell><Str value="l_houseNumberAdd"/><Str value="l_houseNumberAdd"/></HashCell><HashCell><Str value="l_houseNumberAdd7chars"/><Str value="l_houseNumberAdd7chars"/></HashCell><HashCell><Str value="l_houseNumberAddition"/><Str value="l_houseNumberAddition"/></HashCell><HashCell><Str value="l_houseNumberAddition_short"/><Str value="l_houseNumberAddition_short"/></HashCell><HashCell><Str value="l_houseletter"/><Str value="l_houseletter"/></HashCell><HashCell><Str value="l_houseletter_short"/><Str value="l_houseletter_short"/></HashCell><HashCell><Str value="l_housenumber"/><Str value="l_housenumber"/></HashCell><HashCell><Str value="l_housenumber5chars"/><Str value="l_housenumber5chars"/></HashCell><HashCell><Str value="l_housenumber_short"/><Str value="l_housenumber_short"/></HashCell><HashCell><Str value="l_imageGallery"/><Str value="l_imageGallery"/></HashCell><HashCell><Str value="l_info"/><Str value="l_info"/></HashCell><HashCell><Str value="l_initDBFailed"/><Str value="l_initDBFailed"/></HashCell><HashCell><Str value="l_initialize"/><Str value="l_initialize"/></HashCell><HashCell><Str value="l_initialize_text"/><Str value="l_initialize_text"/></HashCell><HashCell><Str value="l_initialize_title"/><Str value="l_initialize_title"/></HashCell><HashCell><Str value="l_initials"/><Str value="l_initials"/></HashCell><HashCell><Str value="l_inserted"/><Str value="l_inserted"/></HashCell><HashCell><Str value="l_inspectionExpirationDate"/><Str value="l_inspectionExpirationDate"/></HashCell><HashCell><Str value="l_instance"/><Str value="l_instance"/></HashCell><HashCell><Str value="l_insuranceDate"/><Str value="l_insuranceDate"/></HashCell><HashCell><Str value="l_internalRemark"/><Str value="l_internalRemark"/></HashCell><HashCell><Str value="l_interpreterCommunicated"/><Str value="l_interpreterCommunicated"/></HashCell><HashCell><Str value="l_interpreterNR"/><Str value="l_interpreterNR"/></HashCell><HashCell><Str value="l_invalid"/><Str value="l_invalid"/></HashCell><HashCell><Str value="l_invalidLoginCredentails"/><Str value="l_invalidLoginCredentails"/></HashCell><HashCell><Str value="l_isNotFilled"/><Str value="l_isNotFilled"/></HashCell><HashCell><Str value="l_keepLocation"/><Str value="l_keepLocation"/></HashCell><HashCell><Str value="l_kind"/><Str value="l_kind"/></HashCell><HashCell><Str value="l_kindOfHandling"/><Str value="l_kindOfHandling"/></HashCell><HashCell><Str value="l_kindOfLabel"/><Str value="l_kindOfLabel"/></HashCell><HashCell><Str value="l_kindOfRoad"/><Str value="l_kindOfRoad"/></HashCell><HashCell><Str value="l_kindOfVehicle"/><Str value="l_kindOfVehicle"/></HashCell><HashCell><Str value="l_label"/><Str value="l_label"/></HashCell><HashCell><Str value="l_labelCheck"/><Str value="l_labelCheck"/></HashCell><HashCell><Str value="l_labelExists"/><Str value="l_labelExists"/></HashCell><HashCell><Str value="l_labelOverview"/><Str value="l_labelOverview"/></HashCell><HashCell><Str value="l_labelPhoto"/><Str value="l_labelPhoto"/></HashCell><HashCell><Str value="l_lastChosen"/><Str value="l_lastChosen"/></HashCell><HashCell><Str value="l_lastChosenOutcome"/><Str value="l_lastChosenOutcome"/></HashCell><HashCell><Str value="l_lastsync"/><Str value="l_lastsync"/></HashCell><HashCell><Str value="l_legalAssistCommunicated"/><Str value="l_legalAssistCommunicated"/></HashCell><HashCell><Str value="l_legalAssistance"/><Str value="l_legalAssistance"/></HashCell><HashCell><Str value="l_legalForm"/><Str value="l_legalForm"/></HashCell><HashCell><Str value="l_licensePlateCountryUnknown"/><Str value="l_licensePlateCountryUnknown"/></HashCell><HashCell><Str value="l_licenseplate"/><Str value="l_licenseplate"/></HashCell><HashCell><Str value="l_licenseplateCountries"/><Str value="l_licenseplateCountries"/></HashCell><HashCell><Str value="l_list"/><Str value="l_list"/></HashCell><HashCell><Str value="l_listNotAvailable"/><Str value="l_listNotAvailable"/></HashCell><HashCell><Str value="l_loadLicenseplate"/><Str value="l_loadLicenseplate"/></HashCell><HashCell><Str value="l_loadPhoto"/><Str value="l_loadPhoto"/></HashCell><HashCell><Str value="l_load_map"/><Str value="l_load_map"/></HashCell><HashCell><Str value="l_loading"/><Str value="l_loading"/></HashCell><HashCell><Str value="l_loadingParam"/><Str value="l_loadingParam"/></HashCell><HashCell><Str value="l_location"/><Str value="l_location"/></HashCell><HashCell><Str value="l_locationFound"/><Str value="l_locationFound"/></HashCell><HashCell><Str value="l_locationVerified"/><Str value="l_locationVerified"/></HashCell><HashCell><Str value="l_logOffDate"/><Str value="l_logOffDate"/></HashCell><HashCell><Str value="l_logOffTime"/><Str value="l_logOffTime"/></HashCell><HashCell><Str value="l_logOn"/><Str value="l_logOn"/></HashCell><HashCell><Str value="l_logOnDate"/><Str value="l_logOnDate"/></HashCell><HashCell><Str value="l_logOnDuration"/><Str value="l_logOnDuration"/></HashCell><HashCell><Str value="l_logOnStatus"/><Str value="l_logOnStatus"/></HashCell><HashCell><Str value="l_logOnTime"/><Str value="l_logOnTime"/></HashCell><HashCell><Str value="l_logOut"/><Str value="l_logOut"/></HashCell><HashCell><Str value="l_loggingOut"/><Str value="l_loggingOut"/></HashCell><HashCell><Str value="l_login"/><Str value="l_login"/></HashCell><HashCell><Str value="l_loginAccount"/><Str value="l_loginAccount"/></HashCell><HashCell><Str value="l_loginAuthorisation"/><Str value="l_loginAuthorisation"/></HashCell><HashCell><Str value="l_loginInstance"/><Str value="l_loginInstance"/></HashCell><HashCell><Str value="l_loginName"/><Str value="l_loginName"/></HashCell><HashCell><Str value="l_loginNoFunctions"/><Str value="l_loginNoFunctions"/></HashCell><HashCell><Str value="l_loginNoOfficerId"/><Str value="l_loginNoOfficerId"/></HashCell><HashCell><Str value="l_loginRegister"/><Str value="l_loginRegister"/></HashCell><HashCell><Str value="l_loginServer"/><Str value="l_loginServer"/></HashCell><HashCell><Str value="l_loginText1"/><Str value="l_loginText1"/></HashCell><HashCell><Str value="l_loginText2"/><Str value="l_loginText2"/></HashCell><HashCell><Str value="l_loginUserIncomplete"/><Str value="l_loginUserIncomplete"/></HashCell><HashCell><Str value="l_loginUserIncorrect"/><Str value="l_loginUserIncorrect"/></HashCell><HashCell><Str value="l_loginUserNotFound"/><Str value="l_loginUserNotFound"/></HashCell><HashCell><Str value="l_loginWelcome"/><Str value="l_loginWelcome"/></HashCell><HashCell><Str value="l_logoff"/><Str value="l_logoff"/></HashCell><HashCell><Str value="l_mandatoryBrand"/><Str value="l_mandatoryBrand"/></HashCell><HashCell><Str value="l_mandatoryClampNumber"/><Str value="l_mandatoryClampNumber"/></HashCell><HashCell><Str value="l_mandatoryLocation"/><Str value="l_mandatoryLocation"/></HashCell><HashCell><Str value="l_mandatoryNHACountryLicense"/><Str value="l_mandatoryNHACountryLicense"/></HashCell><HashCell><Str value="l_mandatoryOptions"/><Str value="l_mandatoryOptions"/></HashCell><HashCell><Str value="l_mandatoryPhoto"/><Str value="l_mandatoryPhoto"/></HashCell><HashCell><Str value="l_mandatoryPhotoClamp"/><Str value="l_mandatoryPhotoClamp"/></HashCell><HashCell><Str value="l_mandatoryPhotoReceipt"/><Str value="l_mandatoryPhotoReceipt"/></HashCell><HashCell><Str value="l_mandatoryPhotoUnclamp"/><Str value="l_mandatoryPhotoUnclamp"/></HashCell><HashCell><Str value="l_mandatoryRegionCodeFrance"/><Str value="l_mandatoryRegionCodeFrance"/></HashCell><HashCell><Str value="l_mandatoryScanUnit"/><Str value="l_mandatoryScanUnit"/></HashCell><HashCell><Str value="l_mandatoryStatement"/><Str value="l_mandatoryStatement"/></HashCell><HashCell><Str value="l_mandatoryVehicle"/><Str value="l_mandatoryVehicle"/></HashCell><HashCell><Str value="l_manual"/><Str value="l_manual"/></HashCell><HashCell><Str value="l_manualLocation"/><Str value="l_manualLocation"/></HashCell><HashCell><Str value="l_map"/><Str value="l_map"/></HashCell><HashCell><Str value="l_markPhotoReceipt"/><Str value="l_markPhotoReceipt"/></HashCell><HashCell><Str value="l_mastercode"/><Str value="l_mastercode"/></HashCell><HashCell><Str value="l_max6Photos"/><Str value="l_max6Photos"/></HashCell><HashCell><Str value="l_max7Photos"/><Str value="l_max7Photos"/></HashCell><HashCell><Str value="l_max8Photos"/><Str value="l_max8Photos"/></HashCell><HashCell><Str value="l_maxPhotosTaken"/><Str value="l_maxPhotosTaken"/></HashCell><HashCell><Str value="l_maxdatetime"/><Str value="l_maxdatetime"/></HashCell><HashCell><Str value="l_mediaGalleryError"/><Str value="l_mediaGalleryError"/></HashCell><HashCell><Str value="l_member"/><Str value="l_member"/></HashCell><HashCell><Str value="l_menu"/><Str value="l_menu"/></HashCell><HashCell><Str value="l_menuSettings"/><Str value="l_menuSettings"/></HashCell><HashCell><Str value="l_minPhotosMandatory"/><Str value="l_minPhotosMandatory"/></HashCell><HashCell><Str value="l_minmaxPhotosMandatory"/><Str value="l_minmaxPhotosMandatory"/></HashCell><HashCell><Str value="l_minutes"/><Str value="l_minutes"/></HashCell><HashCell><Str value="l_moreThen1Area"/><Str value="l_moreThen1Area"/></HashCell><HashCell><Str value="l_moreThenOnePersonFound"/><Str value="l_moreThenOnePersonFound"/></HashCell><HashCell><Str value="l_motorcycle"/><Str value="l_motorcycle"/></HashCell><HashCell><Str value="l_multiUserDevice"/><Str value="l_multiUserDevice"/></HashCell><HashCell><Str value="l_multimedia"/><Str value="l_multimedia"/></HashCell><HashCell><Str value="l_multipleFiltersSet"/><Str value="l_multipleFiltersSet"/></HashCell><HashCell><Str value="l_multipleZipcodes"/><Str value="l_multipleZipcodes"/></HashCell><HashCell><Str value="l_municipality"/><Str value="l_municipality"/></HashCell><HashCell><Str value="l_municipalityOfBirth"/><Str value="l_municipalityOfBirth"/></HashCell><HashCell><Str value="l_mustTakePicures"/><Str value="l_mustTakePicures"/></HashCell><HashCell><Str value="l_myLocation"/><Str value="l_myLocation"/></HashCell><HashCell><Str value="l_name"/><Str value="l_name"/></HashCell><HashCell><Str value="l_nameTaxpayer"/><Str value="l_nameTaxpayer"/></HashCell><HashCell><Str value="l_nan"/><Str value="l_nan"/></HashCell><HashCell><Str value="l_nationality"/><Str value="l_nationality"/></HashCell><HashCell><Str value="l_nearbyAddresses"/><Str value="l_nearbyAddresses"/></HashCell><HashCell><Str value="l_nearbyDevicesPermission"/><Str value="l_nearbyDevicesPermission"/></HashCell><HashCell><Str value="l_nearbyStreets"/><Str value="l_nearbyStreets"/></HashCell><HashCell><Str value="l_nearestHectometerMarker"/><Str value="l_nearestHectometerMarker"/></HashCell><HashCell><Str value="l_newest_ontop"/><Str value="l_newest_above"/></HashCell><HashCell><Str value="l_nex25"/><Str value="l_nex25"/></HashCell><HashCell><Str value="l_next"/><Str value="l_next"/></HashCell><HashCell><Str value="l_nha"/><Str value="l_nha"/></HashCell><HashCell><Str value="l_no"/><Str value="l_no"/></HashCell><HashCell><Str value="l_noActiveCloudFor"/><Str value="l_noActiveCloudFor"/></HashCell><HashCell><Str value="l_noAreaOrMultipleArea"/><Str value="l_noAreaOrMultipleArea"/></HashCell><HashCell><Str value="l_noAuthorization"/><Str value="l_noAuthorization"/></HashCell><HashCell><Str value="l_noBrand"/><Str value="l_noBrand"/></HashCell><HashCell><Str value="l_noCardTypeAvailable"/><Str value="l_noCardTypeAvailable"/></HashCell><HashCell><Str value="l_noCase"/><Str value="l_noCase"/></HashCell><HashCell><Str value="l_noCaseLoaded"/><Str value="l_noCaseLoaded"/></HashCell><HashCell><Str value="l_noCaseTypesFound"/><Str value="l_noCaseTypesFound"/></HashCell><HashCell><Str value="l_noCases"/><Str value="l_noCases"/></HashCell><HashCell><Str value="l_noChangeAvailable"/><Str value="l_noChangeAvailable"/></HashCell><HashCell><Str value="l_noConnection"/><Str value="l_noConnection"/></HashCell><HashCell><Str value="l_noCoordinates"/><Str value="l_noCoordinates"/></HashCell><HashCell><Str value="l_noCoordinatesNearbyStreets"/><Str value="l_noCoordinatesNearbyStreets"/></HashCell><HashCell><Str value="l_noCountriesFound"/><Str value="l_noCountriesFound"/></HashCell><HashCell><Str value="l_noDocument"/><Str value="l_noDocument"/></HashCell><HashCell><Str value="l_noDossierPVCasesFound"/><Str value="l_noDossierPVCasesFound"/></HashCell><HashCell><Str value="l_noExemption"/><Str value="l_noExemption"/></HashCell><HashCell><Str value="l_noFilterSet"/><Str value="l_noFilterSet"/></HashCell><HashCell><Str value="l_noInstances"/><Str value="l_noInstances"/></HashCell><HashCell><Str value="l_noLocation"/><Str value="l_noLocation"/></HashCell><HashCell><Str value="l_noName"/><Str value="l_noName"/></HashCell><HashCell><Str value="l_noNetworkClose"/><Str value="l_noNetworkClose"/></HashCell><HashCell><Str value="l_noNumberCopied"/><Str value="l_noNumberCopied"/></HashCell><HashCell><Str value="l_noOffence"/><Str value="l_noOffence"/></HashCell><HashCell><Str value="l_noOffencesFound"/><Str value="l_noOffencesFound"/></HashCell><HashCell><Str value="l_noOpenTasks"/><Str value="l_noOpenTasks"/></HashCell><HashCell><Str value="l_noOutcome"/><Str value="l_noOutcome"/></HashCell><HashCell><Str value="l_noPersonRegistered"/><Str value="l_noPersonRegistered"/></HashCell><HashCell><Str value="l_noPhotosTaken"/><Str value="l_noPhotosTaken"/></HashCell><HashCell><Str value="l_noPin"/><Str value="l_noPin"/></HashCell><HashCell><Str value="l_noPledge"/><Str value="l_noPledge"/></HashCell><HashCell><Str value="l_noPreviousFines"/><Str value="l_noPreviousFines"/></HashCell><HashCell><Str value="l_noReferenceDataRefresh"/><Str value="l_noReferenceDataRefresh"/></HashCell><HashCell><Str value="l_noServerConnectionRestart"/><Str value="l_noServerConnectionRestart"/></HashCell><HashCell><Str value="l_noSignal"/><Str value="l_noSignal"/></HashCell><HashCell><Str value="l_noStreetFound"/><Str value="l_noStreetFound"/></HashCell><HashCell><Str value="l_noStreetInBAG"/><Str value="l_noStreetInBAG"/></HashCell><HashCell><Str value="l_noTerminalsLocation"/><Str value="l_noTerminalsLocation"/></HashCell><HashCell><Str value="l_noTicketTypes"/><Str value="l_noTicketTypes"/></HashCell><HashCell><Str value="l_noValidDate"/><Str value="l_noValidDate"/></HashCell><HashCell><Str value="l_noValidPlace"/><Str value="l_noValidPlace"/></HashCell><HashCell><Str value="l_noValidPlate"/><Str value="l_noValidPlate"/></HashCell><HashCell><Str value="l_noViolation"/><Str value="l_noViolation"/></HashCell><HashCell><Str value="l_noZipcode"/><Str value="l_noZipcode"/></HashCell><HashCell><Str value="l_no_data_found"/><Str value="l_no_data_found"/></HashCell><HashCell><Str value="l_no_favorites"/><Str value="l_no_favorites"/></HashCell><HashCell><Str value="l_no_media"/><Str value="l_no_media"/></HashCell><HashCell><Str value="l_no_menu_authorization"/><Str value="l_no_menu_authorization"/></HashCell><HashCell><Str value="l_no_notes"/><Str value="l_no_notes"/></HashCell><HashCell><Str value="l_no_recent"/><Str value="l_no_recent"/></HashCell><HashCell><Str value="l_noaddressfound"/><Str value="l_noaddressfound"/></HashCell><HashCell><Str value="l_notAnSSN"/><Str value="l_notAnSSN"/></HashCell><HashCell><Str value="l_notAvailable"/><Str value="l_notAvailable"/></HashCell><HashCell><Str value="l_notRegistered"/><Str value="l_notRegistered"/></HashCell><HashCell><Str value="l_notSameLocation"/><Str value="l_notSameLocation"/></HashCell><HashCell><Str value="l_notValidZipCode"/><Str value="l_notValidZipCode"/></HashCell><HashCell><Str value="l_not_activated"/><Str value="l_not_activated"/></HashCell><HashCell><Str value="l_not_all_fields_filled"/><Str value="l_not_all_fields_filled"/></HashCell><HashCell><Str value="l_not_set"/><Str value="l_not_set"/></HashCell><HashCell><Str value="l_notariff"/><Str value="l_notariff"/></HashCell><HashCell><Str value="l_notariff_star"/><Str value="l_notariff_star"/></HashCell><HashCell><Str value="l_notfound"/><Str value="l_notfound"/></HashCell><HashCell><Str value="l_nothingFound"/><Str value="l_nothingFound"/></HashCell><HashCell><Str value="l_nozonesassigned"/><Str value="l_nozonesassigned"/></HashCell><HashCell><Str value="l_nr"/><Str value="l_nr"/></HashCell><HashCell><Str value="l_number"/><Str value="l_number"/></HashCell><HashCell><Str value="l_numberm2"/><Str value="l_numberm2"/></HashCell><HashCell><Str value="l_objectdata"/><Str value="l_objectdata"/></HashCell><HashCell><Str value="l_objectnumber"/><Str value="l_objectnumber"/></HashCell><HashCell><Str value="l_objectsFound"/><Str value="l_objectsFound"/></HashCell><HashCell><Str value="l_objectsyncCasemanagement"/><Str value="l_objectsyncCasemanagement"/></HashCell><HashCell><Str value="l_objectsyncGlobal"/><Str value="l_objectsyncGlobal"/></HashCell><HashCell><Str value="l_objectsyncHectometermarker"/><Str value="l_objectsyncHectometermarker"/></HashCell><HashCell><Str value="l_objectsyncInstance"/><Str value="l_objectsyncInstance"/></HashCell><HashCell><Str value="l_objectsyncKilometermarker"/><Str value="l_objectsyncKilometermarker"/></HashCell><HashCell><Str value="l_objectsyncLocation"/><Str value="l_objectsyncLocation"/></HashCell><HashCell><Str value="l_objectsyncOffence"/><Str value="l_objectsyncOffence"/></HashCell><HashCell><Str value="l_objectsyncPerson"/><Str value="l_objectsyncPerson"/></HashCell><HashCell><Str value="l_objectsyncPlanning"/><Str value="l_objectsyncPlanning"/></HashCell><HashCell><Str value="l_objectsyncReference"/><Str value="l_objectsyncReference"/></HashCell><HashCell><Str value="l_objectsyncScanUnit"/><Str value="l_objectsyncScanUnit"/></HashCell><HashCell><Str value="l_objectsyncTheme"/><Str value="l_objectsyncTheme"/></HashCell><HashCell><Str value="l_objectsyncVehicle"/><Str value="l_objectsyncVehicle"/></HashCell><HashCell><Str value="l_observation"/><Str value="l_observation"/></HashCell><HashCell><Str value="l_observationTitel"/><Str value="l_observationTitel"/></HashCell><HashCell><Str value="l_offence"/><Str value="l_offence"/></HashCell><HashCell><Str value="l_offenceCategory"/><Str value="l_offenceCategory"/></HashCell><HashCell><Str value="l_offenceCommunicated"/><Str value="l_offenceCommunicated"/></HashCell><HashCell><Str value="l_officerNotFound"/><Str value="l_officerNotFound"/></HashCell><HashCell><Str value="l_officerNotFoundMuni"/><Str value="l_officerNotFoundMuni"/></HashCell><HashCell><Str value="l_officerNumber"/><Str value="l_officerNumber"/></HashCell><HashCell><Str value="l_officerNumberPrint"/><Str value="l_officerNumberPrint"/></HashCell><HashCell><Str value="l_officer_findings"/><Str value="l_officer_findings"/></HashCell><HashCell><Str value="l_officer_observation"/><Str value="l_officer_observation"/></HashCell><HashCell><Str value="l_oldest_ontop"/><Str value="l_oldest_ontop"/></HashCell><HashCell><Str value="l_onStreetParkingCheck"/><Str value="l_onStreetParkingCheck"/></HashCell><HashCell><Str value="l_onstreetpayment"/><Str value="l_onstreetpayment"/></HashCell><HashCell><Str value="l_open"/><Str value="l_open"/></HashCell><HashCell><Str value="l_openCase"/><Str value="l_openCase"/></HashCell><HashCell><Str value="l_openPDF"/><Str value="l_openPDF"/></HashCell><HashCell><Str value="l_openTasks"/><Str value="l_openTasks"/></HashCell><HashCell><Str value="l_options"/><Str value="l_options"/></HashCell><HashCell><Str value="l_optionsVariables"/><Str value="l_optionsVariables"/></HashCell><HashCell><Str value="l_oranje"/><Str value="l_oranje"/></HashCell><HashCell><Str value="l_other"/><Str value="l_other"/></HashCell><HashCell><Str value="l_otherSignals"/><Str value="l_otherSignals"/></HashCell><HashCell><Str value="l_outOfMemory"/><Str value="l_outOfMemory"/></HashCell><HashCell><Str value="l_outbox"/><Str value="l_outbox"/></HashCell><HashCell><Str value="l_outcome"/><Str value="l_outcome"/></HashCell><HashCell><Str value="l_overview"/><Str value="l_overview"/></HashCell><HashCell><Str value="l_overviewLabel"/><Str value="l_overviewLabel"/></HashCell><HashCell><Str value="l_overviewPhoto"/><Str value="l_overviewPhoto"/></HashCell><HashCell><Str value="l_overviewTask"/><Str value="l_overviewTask"/></HashCell><HashCell><Str value="l_ownStatement"/><Str value="l_ownStatement"/></HashCell><HashCell><Str value="l_ownerRegistrationDate"/><Str value="l_ownerRegistrationDate"/></HashCell><HashCell><Str value="l_ownerRegistrationDatePrint"/><Str value="l_ownerRegistrationDatePrint"/></HashCell><HashCell><Str value="l_paars"/><Str value="l_paars"/></HashCell><HashCell><Str value="l_paidParkingZone"/><Str value="l_paidParkingZone"/></HashCell><HashCell><Str value="l_parking"/><Str value="l_parking"/></HashCell><HashCell><Str value="l_parkingCardNumber"/><Str value="l_parkingCardNumber"/></HashCell><HashCell><Str value="l_parkingCardOutcome"/><Str value="l_parkingCardOutcome"/></HashCell><HashCell><Str value="l_parkingPenalty"/><Str value="l_parkingPenalty"/></HashCell><HashCell><Str value="l_parkingServicefailed"/><Str value="l_parkingServicefailed"/></HashCell><HashCell><Str value="l_parking_expired"/><Str value="l_parking_expired"/></HashCell><HashCell><Str value="l_parking_invalid"/><Str value="l_parking_invalid"/></HashCell><HashCell><Str value="l_parking_unavailable_end"/><Str value="l_parking_unavailable_end"/></HashCell><HashCell><Str value="l_parking_unavailable_start"/><Str value="l_parking_unavailable_start"/></HashCell><HashCell><Str value="l_parking_valid"/><Str value="l_parking_valid"/></HashCell><HashCell><Str value="l_parkingcard"/><Str value="l_parkingcard"/></HashCell><HashCell><Str value="l_parkingcheck"/><Str value="l_parkingCheck"/></HashCell><HashCell><Str value="l_password"/><Str value="l_password"/></HashCell><HashCell><Str value="l_paste"/><Str value="l_paste"/></HashCell><HashCell><Str value="l_pedestrian"/><Str value="l_pedestrian"/></HashCell><HashCell><Str value="l_permit"/><Str value="l_permit"/></HashCell><HashCell><Str value="l_permitName"/><Str value="l_permitName"/></HashCell><HashCell><Str value="l_permitTypeDesc"/><Str value="l_permitTypeDesc"/></HashCell><HashCell><Str value="l_permit_invalid"/><Str value="l_permit_invalid"/></HashCell><HashCell><Str value="l_permit_valid"/><Str value="l_permit_valid"/></HashCell><HashCell><Str value="l_permitcheck"/><Str value="l_permitcheck"/></HashCell><HashCell><Str value="l_permitnumber"/><Str value="l_permitnumber"/></HashCell><HashCell><Str value="l_person"/><Str value="l_person"/></HashCell><HashCell><Str value="l_personData"/><Str value="l_persondata"/></HashCell><HashCell><Str value="l_personUnderInvestigation"/><Str value="l_personUnderInvestigation"/></HashCell><HashCell><Str value="l_photo"/><Str value="l_photo"/></HashCell><HashCell><Str value="l_photoNotFound"/><Str value="l_photoNotFound"/></HashCell><HashCell><Str value="l_photoProcessingFailed"/><Str value="l_photoProcessingFailed"/></HashCell><HashCell><Str value="l_photoSelectFailed"/><Str value="l_photoSelectFailed"/></HashCell><HashCell><Str value="l_photos"/><Str value="l_photos"/></HashCell><HashCell><Str value="l_pinNotCorrect"/><Str value="l_pinNotCorrect"/></HashCell><HashCell><Str value="l_pinNotUsable"/><Str value="l_pinNotUsable"/></HashCell><HashCell><Str value="l_pinsDoNotMatch"/><Str value="l_pinsDoNotMatch"/></HashCell><HashCell><Str value="l_placeofEstablishment"/><Str value="l_placeofEstablishment"/></HashCell><HashCell><Str value="l_placeofresidency"/><Str value="l_placeofresidency"/></HashCell><HashCell><Str value="l_plateNotValid"/><Str value="l_plateNotValid"/></HashCell><HashCell><Str value="l_plate_format"/><Str value="l_plate_format"/></HashCell><HashCell><Str value="l_platesFound"/><Str value="l_platesFound"/></HashCell><HashCell><Str value="l_pledge"/><Str value="l_pledge"/></HashCell><HashCell><Str value="l_policeofficeremail"/><Str value="l_policeofficeremail"/></HashCell><HashCell><Str value="l_preferences"/><Str value="l_preferences"/></HashCell><HashCell><Str value="l_prefix"/><Str value="l_prefix"/></HashCell><HashCell><Str value="l_previousFines"/><Str value="l_previousFines"/></HashCell><HashCell><Str value="l_priceOneHourPark"/><Str value="l_priceOneHourPark"/></HashCell><HashCell><Str value="l_print"/><Str value="l_print"/></HashCell><HashCell><Str value="l_printAmountPayed"/><Str value="l_printAmountPayed"/></HashCell><HashCell><Str value="l_printTestLabel"/><Str value="l_printTestLabel"/></HashCell><HashCell><Str value="l_print_observation"/><Str value="l_print_observation"/></HashCell><HashCell><Str value="l_printerType"/><Str value="l_printerType"/></HashCell><HashCell><Str value="l_printing"/><Str value="l_printing"/></HashCell><HashCell><Str value="l_process"/><Str value="l_process"/></HashCell><HashCell><Str value="l_processing"/><Str value="l_processing"/></HashCell><HashCell><Str value="l_production"/><Str value="l_production"/></HashCell><HashCell><Str value="l_production_pt"/><Str value="l_production_pt"/></HashCell><HashCell><Str value="l_prohibitions"/><Str value="l_prohibitions"/></HashCell><HashCell><Str value="l_provider"/><Str value="l_provider"/></HashCell><HashCell><Str value="l_publishedBy"/><Str value="l_publishedBy"/></HashCell><HashCell><Str value="l_putOnLabel"/><Str value="l_putOnLabel"/></HashCell><HashCell><Str value="l_pvFailed"/><Str value="l_pvFailed"/></HashCell><HashCell><Str value="l_pvId"/><Str value="l_pvId"/></HashCell><HashCell><Str value="l_pvNotAvailable"/><Str value="l_pvNotAvailable"/></HashCell><HashCell><Str value="l_questions"/><Str value="l_questions"/></HashCell><HashCell><Str value="l_queueLengthTimerOn"/><Str value="l_queueLengthTimerOn"/></HashCell><HashCell><Str value="l_radius"/><Str value="l_radius"/></HashCell><HashCell><Str value="l_receipt"/><Str value="l_receipt"/></HashCell><HashCell><Str value="l_receiptNumber"/><Str value="l_receiptNumber"/></HashCell><HashCell><Str value="l_receivedAt"/><Str value="l_receivedAt"/></HashCell><HashCell><Str value="l_recheck"/><Str value="l_recheck"/></HashCell><HashCell><Str value="l_reformatDateFailed"/><Str value="l_reformatDateFailed"/></HashCell><HashCell><Str value="l_refresh"/><Str value="l_refresh"/></HashCell><HashCell><Str value="l_refreshGPS"/><Str value="l_refreshGPS"/></HashCell><HashCell><Str value="l_refreshLocation"/><Str value="l_refreshLocation"/></HashCell><HashCell><Str value="l_refreshTime"/><Str value="l_refreshTime"/></HashCell><HashCell><Str value="l_reg_date"/><Str value="l_reg_date"/></HashCell><HashCell><Str value="l_regionCode"/><Str value="l_regionCode"/></HashCell><HashCell><Str value="l_register"/><Str value="l_register"/></HashCell><HashCell><Str value="l_register_error"/><Str value="l_register_error"/></HashCell><HashCell><Str value="l_register_success"/><Str value="l_register_success"/></HashCell><HashCell><Str value="l_registerconcept"/><Str value="l_registerconcept"/></HashCell><HashCell><Str value="l_registeredByOrganization"/><Str value="l_registeredByOrganization"/></HashCell><HashCell><Str value="l_registering_device"/><Str value="l_registering_device"/></HashCell><HashCell><Str value="l_registrationDate"/><Str value="l_registrationDate"/></HashCell><HashCell><Str value="l_registrationDatePrint"/><Str value="l_registrationDatePrint"/></HashCell><HashCell><Str value="l_registrationDateTime"/><Str value="l_registrationDateTime"/></HashCell><HashCell><Str value="l_registrationNotFound"/><Str value="l_registrationNotFound"/></HashCell><HashCell><Str value="l_registrationTitle"/><Str value="l_registrationTitle"/></HashCell><HashCell><Str value="l_regulatedTimeConsumption"/><Str value="l_regulatedTimeConsumption"/></HashCell><HashCell><Str value="l_regulation"/><Str value="l_regulation"/></HashCell><HashCell><Str value="l_relatedcase"/><Str value="l_relatedcase"/></HashCell><HashCell><Str value="l_remark"/><Str value="l_remark"/></HashCell><HashCell><Str value="l_removalFailed"/><Str value="l_removalFailed"/></HashCell><HashCell><Str value="l_remove"/><Str value="l_remove"/></HashCell><HashCell><Str value="l_removePhoto"/><Str value="l_removePhoto"/></HashCell><HashCell><Str value="l_removeReg"/><Str value="l_removeReg"/></HashCell><HashCell><Str value="l_removed"/><Str value="l_removed"/></HashCell><HashCell><Str value="l_removefilter"/><Str value="l_removefilter"/></HashCell><HashCell><Str value="l_reported"/><Str value="l_reported"/></HashCell><HashCell><Str value="l_rescan"/><Str value="l_rescan"/></HashCell><HashCell><Str value="l_resendPhotos"/><Str value="l_resendPhotos"/></HashCell><HashCell><Str value="l_resendThumbnail"/><Str value="l_resendThumbnail"/></HashCell><HashCell><Str value="l_reset"/><Str value="l_reset"/></HashCell><HashCell><Str value="l_resetAfter5Tries"/><Str value="l_resetAfter5Tries"/></HashCell><HashCell><Str value="l_resetDBandReload"/><Str value="l_resetDBandReload"/></HashCell><HashCell><Str value="l_resetDatabase"/><Str value="l_resetDatabase"/></HashCell><HashCell><Str value="l_resetPin"/><Str value="l_resetPin"/></HashCell><HashCell><Str value="l_resetUser"/><Str value="l_resetUser"/></HashCell><HashCell><Str value="l_restoreLayout"/><Str value="l_restoreLayout"/></HashCell><HashCell><Str value="l_result"/><Str value="l_result"/></HashCell><HashCell><Str value="l_resume"/><Str value="l_resume"/></HashCell><HashCell><Str value="l_resyncFailed"/><Str value="l_resyncFailed"/></HashCell><HashCell><Str value="l_retrieve"/><Str value="l_retrieve"/></HashCell><HashCell><Str value="l_retrieveNewCase"/><Str value="l_retrieveNewCase"/></HashCell><HashCell><Str value="l_retrievingNewCases"/><Str value="l_retrievingNewCases"/></HashCell><HashCell><Str value="l_retryServerconnection"/><Str value="l_retryServerconnection"/></HashCell><HashCell><Str value="l_rightType"/><Str value="l_rightType"/></HashCell><HashCell><Str value="l_rightTypeDetail"/><Str value="l_rightTypeDetail"/></HashCell><HashCell><Str value="l_rightsNotReadConfirmation"/><Str value="l_rightsNotReadConfirmation"/></HashCell><HashCell><Str value="l_roadsigns"/><Str value="l_roadsigns"/></HashCell><HashCell><Str value="l_route"/><Str value="l_route"/></HashCell><HashCell><Str value="l_routeInfo"/><Str value="l_routeInfo"/></HashCell><HashCell><Str value="l_rows"/><Str value="l_rows"/></HashCell><HashCell><Str value="l_sameAddress"/><Str value="l_sameAddress"/></HashCell><HashCell><Str value="l_sanction"/><Str value="l_sanction"/></HashCell><HashCell><Str value="l_save"/><Str value="l_save"/></HashCell><HashCell><Str value="l_saveAndExit"/><Str value="l_saveAndExit"/></HashCell><HashCell><Str value="l_save_data"/><Str value="l_save_data"/></HashCell><HashCell><Str value="l_saved"/><Str value="l_saved"/></HashCell><HashCell><Str value="l_sbiCode"/><Str value="l_sbiCode"/></HashCell><HashCell><Str value="l_sbiCodeDescription"/><Str value="l_sbiCodeDescription"/></HashCell><HashCell><Str value="l_scan"/><Str value="l_scan"/></HashCell><HashCell><Str value="l_scanEnableAutoRestart"/><Str value="l_scanEnableAutoRestart"/></HashCell><HashCell><Str value="l_scanFailure"/><Str value="l_scanFailure"/></HashCell><HashCell><Str value="l_scan_delay"/><Str value="l_scan_delay"/></HashCell><HashCell><Str value="l_scancountry"/><Str value="l_scancountry"/></HashCell><HashCell><Str value="l_scanner_light"/><Str value="l_scanner_light"/></HashCell><HashCell><Str value="l_sealbagNumber"/><Str value="l_sealbagNumber"/></HashCell><HashCell><Str value="l_search"/><Str value="l_search"/></HashCell><HashCell><Str value="l_searchAddress"/><Str value="l_searchAddress"/></HashCell><HashCell><Str value="l_searchExact"/><Str value="l_searchExact"/></HashCell><HashCell><Str value="l_searchLetter"/><Str value="l_searchLetter"/></HashCell><HashCell><Str value="l_searchNumber"/><Str value="l_searchNumber"/></HashCell><HashCell><Str value="l_searchPlatesBy"/><Str value="l_searchPlatesBy"/></HashCell><HashCell><Str value="l_search_numberorsurname"/><Str value="l_search_numberorsurname"/></HashCell><HashCell><Str value="l_search_printers"/><Str value="l_search_printers"/></HashCell><HashCell><Str value="l_searchstreetandestablishmentplace"/><Str value="l_searchstreetandestablishmentplace"/></HashCell><HashCell><Str value="l_secondOfficer"/><Str value="l_secondOfficer"/></HashCell><HashCell><Str value="l_section"/><Str value="l_section"/></HashCell><HashCell><Str value="l_sector"/><Str value="l_sector"/></HashCell><HashCell><Str value="l_sectorDesc"/><Str value="l_sectorDesc"/></HashCell><HashCell><Str value="l_select"/><Str value="l_select"/></HashCell><HashCell><Str value="l_selectFolder"/><Str value="l_selectFolder"/></HashCell><HashCell><Str value="l_selectPrinter"/><Str value="l_selectPrinter"/></HashCell><HashCell><Str value="l_selectTeam"/><Str value="l_selectTeam"/></HashCell><HashCell><Str value="l_selectall"/><Str value="l_selectall"/></HashCell><HashCell><Str value="l_selectedPrinter"/><Str value="l_selectedPrinter"/></HashCell><HashCell><Str value="l_selectscancountry"/><Str value="l_selectscancountry"/></HashCell><HashCell><Str value="l_send"/><Str value="l_send"/></HashCell><HashCell><Str value="l_send1LabelPhoto"/><Str value="l_send1LabelPhoto"/></HashCell><HashCell><Str value="l_send1RemovalPhoto"/><Str value="l_send1RemovalPhoto"/></HashCell><HashCell><Str value="l_sendPhotos"/><Str value="l_sendPhotos"/></HashCell><HashCell><Str value="l_sendRegistration"/><Str value="l_sendRegistration"/></HashCell><HashCell><Str value="l_sendTaskData"/><Str value="l_sendTaskData"/></HashCell><HashCell><Str value="l_server"/><Str value="l_server"/></HashCell><HashCell><Str value="l_serverError"/><Str value="l_serverError"/></HashCell><HashCell><Str value="l_serviceDidNotRespond"/><Str value="l_serviceDidNotRespond"/></HashCell><HashCell><Str value="l_serviceError"/><Str value="l_serviceError"/></HashCell><HashCell><Str value="l_serviceErrorRetrieveList"/><Str value="l_serviceErrorRetrieveList"/></HashCell><HashCell><Str value="l_service_show_time"/><Str value="l_service_show_time"/></HashCell><HashCell><Str value="l_setCurrentTime"/><Str value="l_setCurrentTime"/></HashCell><HashCell><Str value="l_setReceipt"/><Str value="l_setReceipt"/></HashCell><HashCell><Str value="l_settings"/><Str value="l_settings"/></HashCell><HashCell><Str value="l_shift"/><Str value="l_shift"/></HashCell><HashCell><Str value="l_shift_A"/><Str value="l_shift_A"/></HashCell><HashCell><Str value="l_shift_D"/><Str value="l_shift_D"/></HashCell><HashCell><Str value="l_shift_Z"/><Str value="l_shift_Z"/></HashCell><HashCell><Str value="l_signal"/><Str value="l_signal"/></HashCell><HashCell><Str value="l_signalServicefailed"/><Str value="l_signalServicefailed"/></HashCell><HashCell><Str value="l_socialsecuritynr"/><Str value="l_socialsecuritynr"/></HashCell><HashCell><Str value="l_start"/><Str value="l_start"/></HashCell><HashCell><Str value="l_startApp"/><Str value="l_startApp"/></HashCell><HashCell><Str value="l_startDate"/><Str value="l_startDate"/></HashCell><HashCell><Str value="l_startGrip"/><Str value="l_startGrip"/></HashCell><HashCell><Str value="l_startTime"/><Str value="l_startTime"/></HashCell><HashCell><Str value="l_started"/><Str value="l_started"/></HashCell><HashCell><Str value="l_state"/><Str value="l_state"/></HashCell><HashCell><Str value="l_state100chars"/><Str value="l_state100chars"/></HashCell><HashCell><Str value="l_statement"/><Str value="l_statement"/></HashCell><HashCell><Str value="l_statementRecordedBy"/><Str value="l_statementRecordedBy"/></HashCell><HashCell><Str value="l_statistics"/><Str value="l_statistics"/></HashCell><HashCell><Str value="l_status"/><Str value="l_status"/></HashCell><HashCell><Str value="l_statusClaimed"/><Str value="l_statusClaimed"/></HashCell><HashCell><Str value="l_statusControl_F"/><Str value="l_statusControl_F"/></HashCell><HashCell><Str value="l_statusControl_K"/><Str value="l_statusControl_K"/></HashCell><HashCell><Str value="l_statusControl_L"/><Str value="l_statusControl_L"/></HashCell><HashCell><Str value="l_statusControl_O"/><Str value="l_statusControl_O"/></HashCell><HashCell><Str value="l_status_unknown"/><Str value="l_status_unknown"/></HashCell><HashCell><Str value="l_street"/><Str value="l_street"/></HashCell><HashCell><Str value="l_street24chars"/><Str value="l_street24chars"/></HashCell><HashCell><Str value="l_streetAtNumber"/><Str value="l_streetAtNumber"/></HashCell><HashCell><Str value="l_streetNotDetermined"/><Str value="l_streetNotDetermined"/></HashCell><HashCell><Str value="l_streetnumber"/><Str value="l_streetnumber"/></HashCell><HashCell><Str value="l_streetpropositions"/><Str value="l_streetpropositions"/></HashCell><HashCell><Str value="l_subjectnumber"/><Str value="l_subjectnumber"/></HashCell><HashCell><Str value="l_successful"/><Str value="l_successful"/></HashCell><HashCell><Str value="l_surname"/><Str value="l_surname"/></HashCell><HashCell><Str value="l_swipe"/><Str value="l_swipe"/></HashCell><HashCell><Str value="l_syncError"/><Str value="l_syncError"/></HashCell><HashCell><Str value="l_syncLast"/><Str value="l_syncLast"/></HashCell><HashCell><Str value="l_sync_end"/><Str value="l_sync_end"/></HashCell><HashCell><Str value="l_sync_finished"/><Str value="l_sync_finished"/></HashCell><HashCell><Str value="l_sync_start"/><Str value="l_sync_start"/></HashCell><HashCell><Str value="l_sync_status"/><Str value="l_sync_status"/></HashCell><HashCell><Str value="l_synchronization"/><Str value="l_synchronization"/></HashCell><HashCell><Str value="l_synchronize"/><Str value="l_synchronize"/></HashCell><HashCell><Str value="l_syncinit"/><Str value="l_syncinit"/></HashCell><HashCell><Str value="l_tariff"/><Str value="l_tariff"/></HashCell><HashCell><Str value="l_tariffgroup"/><Str value="l_tariffgroup"/></HashCell><HashCell><Str value="l_task"/><Str value="l_task"/></HashCell><HashCell><Str value="l_taskFault"/><Str value="l_taskFault"/></HashCell><HashCell><Str value="l_taskoutcomeNotFound"/><Str value="l_taskoutcomeNotFound"/></HashCell><HashCell><Str value="l_tasks"/><Str value="l_tasks"/></HashCell><HashCell><Str value="l_tax"/><Str value="l_tax"/></HashCell><HashCell><Str value="l_taxi"/><Str value="l_taxi"/></HashCell><HashCell><Str value="l_team"/><Str value="l_team"/></HashCell><HashCell><Str value="l_templateName"/><Str value="l_templateName"/></HashCell><HashCell><Str value="l_tenant"/><Str value="l_tenant"/></HashCell><HashCell><Str value="l_terminal"/><Str value="l_terminal"/></HashCell><HashCell><Str value="l_test"/><Str value="l_test"/></HashCell><HashCell><Str value="l_text"/><Str value="l_text"/></HashCell><HashCell><Str value="l_thirdOfficer"/><Str value="l_thirdOfficer"/></HashCell><HashCell><Str value="l_ticketType"/><Str value="l_ticketType"/></HashCell><HashCell><Str value="l_ticket_nr"/><Str value="l_ticket_nr"/></HashCell><HashCell><Str value="l_time"/><Str value="l_time"/></HashCell><HashCell><Str value="l_timeArrived"/><Str value="l_timeArrived"/></HashCell><HashCell><Str value="l_timeOutException"/><Str value="l_timeOutException"/></HashCell><HashCell><Str value="l_timeRestriction"/><Str value="l_timeRestriction"/></HashCell><HashCell><Str value="l_timeScanned"/><Str value="l_timeScanned"/></HashCell><HashCell><Str value="l_timeToArrive"/><Str value="l_timeToArrive"/></HashCell><HashCell><Str value="l_tokenExpired"/><Str value="l_tokenExpired"/></HashCell><HashCell><Str value="l_totalDue"/><Str value="l_totalDue"/></HashCell><HashCell><Str value="l_touchIdFail"/><Str value="l_touchIdFail"/></HashCell><HashCell><Str value="l_touchIdSucces"/><Str value="l_touchIdSucces"/></HashCell><HashCell><Str value="l_touchIdWillNotUse"/><Str value="l_touchIdWillNotUse"/></HashCell><HashCell><Str value="l_tow"/><Str value="l_tow"/></HashCell><HashCell><Str value="l_trackDown"/><Str value="l_trackDown"/></HashCell><HashCell><Str value="l_trackDownDesc"/><Str value="l_trackDownDesc"/></HashCell><HashCell><Str value="l_translationLanguage"/><Str value="l_translationLanguage"/></HashCell><HashCell><Str value="l_transpondercardcode"/><Str value="l_transpondercardcode"/></HashCell><HashCell><Str value="l_truck"/><Str value="l_truck"/></HashCell><HashCell><Str value="l_twoPhotosMandatory"/><Str value="l_twoPhotosMandatory"/></HashCell><HashCell><Str value="l_type"/><Str value="l_type"/></HashCell><HashCell><Str value="l_typeCode"/><Str value="l_typeCode"/></HashCell><HashCell><Str value="l_typeTextHere"/><Str value="l_typeTextHere"/></HashCell><HashCell><Str value="l_unClamp"/><Str value="l_unClamp"/></HashCell><HashCell><Str value="l_unclaimed"/><Str value="l_unclaimed"/></HashCell><HashCell><Str value="l_unclampRequestSetOn"/><Str value="l_unclampRequestSetOn"/></HashCell><HashCell><Str value="l_unknown"/><Str value="l_unknown"/></HashCell><HashCell><Str value="l_unpair"/><Str value="l_unpair"/></HashCell><HashCell><Str value="l_updateAvailable"/><Str value="l_updateAvailable"/></HashCell><HashCell><Str value="l_updateAvailableMDM"/><Str value="l_updateAvailableMDM"/></HashCell><HashCell><Str value="l_updateAvailableStoreMDM"/><Str value="l_updateAvailableStoreMDM"/></HashCell><HashCell><Str value="l_uploadCaseFailedRetry"/><Str value="l_uploadCaseFailedRetry"/></HashCell><HashCell><Str value="l_usage"/><Str value="l_usage"/></HashCell><HashCell><Str value="l_user"/><Str value="l_user"/></HashCell><HashCell><Str value="l_userMustBeEmail"/><Str value="l_userMustBeEmail"/></HashCell><HashCell><Str value="l_userNotInInstance"/><Str value="l_userNotInInstance"/></HashCell><HashCell><Str value="l_userPVNotExists"/><Str value="l_userPVNotExists"/></HashCell><HashCell><Str value="l_username"/><Str value="l_username"/></HashCell><HashCell><Str value="l_usesInterpreter"/><Str value="l_usesInterpreter"/></HashCell><HashCell><Str value="l_usesLegalAssistance"/><Str value="l_usesLegalAssistance"/></HashCell><HashCell><Str value="l_valid"/><Str value="l_valid"/></HashCell><HashCell><Str value="l_validFrom"/><Str value="l_validFrom"/></HashCell><HashCell><Str value="l_validParkingRight"/><Str value="l_validParkingRight"/></HashCell><HashCell><Str value="l_validTo"/><Str value="l_validTo"/></HashCell><HashCell><Str value="l_validateVehicleMessage"/><Str value="l_validateVehicleMessage"/></HashCell><HashCell><Str value="l_validateVehicleOr"/><Str value="l_validateVehicleOr"/></HashCell><HashCell><Str value="l_validationNeeded"/><Str value="l_validationNeeded"/></HashCell><HashCell><Str value="l_validityMessage"/><Str value="l_validityMessage"/></HashCell><HashCell><Str value="l_validparkingtime"/><Str value="l_validparkingtime"/></HashCell><HashCell><Str value="l_van"/><Str value="l_van"/></HashCell><HashCell><Str value="l_variables"/><Str value="l_variables"/></HashCell><HashCell><Str value="l_vehicle"/><Str value="l_vehicle"/></HashCell><HashCell><Str value="l_vehicleDetails"/><Str value="l_vehicleDetails"/></HashCell><HashCell><Str value="l_vehicleInformation"/><Str value="l_vehicleInformation"/></HashCell><HashCell><Str value="l_vehicleServicefailed"/><Str value="l_vehicleServicefailed"/></HashCell><HashCell><Str value="l_vehicleTypeGroup"/><Str value="l_vehicleTypeGroup"/></HashCell><HashCell><Str value="l_vehicle_invalid"/><Str value="l_vehicle_invalid"/></HashCell><HashCell><Str value="l_vehicle_law"/><Str value="l_vehicle_law"/></HashCell><HashCell><Str value="l_vehicletype"/><Str value="l_vehicletype"/></HashCell><HashCell><Str value="l_verifyAddress"/><Str value="l_verifyAddress"/></HashCell><HashCell><Str value="l_verifyPin"/><Str value="l_verifyPin"/></HashCell><HashCell><Str value="l_verifyTouchId"/><Str value="l_verifyTouchId"/></HashCell><HashCell><Str value="l_version"/><Str value="l_version"/></HashCell><HashCell><Str value="l_vessel"/><Str value="l_vessel"/></HashCell><HashCell><Str value="l_wait"/><Str value="l_wait"/></HashCell><HashCell><Str value="l_wantToLogOn"/><Str value="l_wantToLogOn"/></HashCell><HashCell><Str value="l_wantToRemoveCase"/><Str value="l_wantToRemoveCase"/></HashCell><HashCell><Str value="l_wantToRemovePhoto"/><Str value="l_wantToRemovePhoto"/></HashCell><HashCell><Str value="l_wantToSaveContinueOrTryAgain"/><Str value="l_wantToSaveContinueOrTryAgain"/></HashCell><HashCell><Str value="l_wantToTryAgain"/><Str value="l_wantToTryAgain"/></HashCell><HashCell><Str value="l_wantToUsePin"/><Str value="l_wantToUsePin"/></HashCell><HashCell><Str value="l_wantToUseTouchId"/><Str value="l_wantToUseTouchId"/></HashCell><HashCell><Str value="l_wanted"/><Str value="l_wanted"/></HashCell><HashCell><Str value="l_warning"/><Str value="l_warning"/></HashCell><HashCell><Str value="l_warningMessage"/><Str value="l_warningMessage"/></HashCell><HashCell><Str value="l_withdraw"/><Str value="l_withdraw"/></HashCell><HashCell><Str value="l_writeExtraTicket"/><Str value="l_writeExtraTicket"/></HashCell><HashCell><Str value="l_xTerminalsLocation"/><Str value="l_xTerminalsLocation"/></HashCell><HashCell><Str value="l_yearofbirth"/><Str value="l_yearofbirth"/></HashCell><HashCell><Str value="l_yes"/><Str value="l_yes"/></HashCell><HashCell><Str value="l_zipCode"/><Str value="l_zipCode"/></HashCell><HashCell><Str value="l_zipCode12chars"/><Str value="l_zipCode12chars"/></HashCell><HashCell><Str value="l_zoneCode"/><Str value="l_zoneCode"/></HashCell><HashCell><Str value="l_zoneDesc"/><Str value="l_zoneDesc"/></HashCell><HashCell><Str value="open_settings"/><Str value="Open Settings"/></HashCell><HashCell><Str value="otherDocuments"/><Str value="Other Documents"/></HashCell><HashCell><Str value="passport"/><Str value="Passport"/></HashCell><HashCell><Str value="pl_banDurationDays"/><Str value="pl_banDurationDays"/></HashCell><HashCell><Str value="pl_banDurationMonths"/><Str value="pl_banDurationMonths"/></HashCell><HashCell><Str value="pl_banDurationWeeks"/><Str value="pl_banDurationWeeks"/></HashCell><HashCell><Str value="pl_casetime"/><Str value="pl_casetime"/></HashCell><HashCell><Str value="pl_char_limit"/><Str value="pl_char_limit"/></HashCell><HashCell><Str value="pl_checkCard"/><Str value="pl_checkCard"/></HashCell><HashCell><Str value="pl_chooseNearbyStreets"/><Str value="pl_chooseNearbyStreets"/></HashCell><HashCell><Str value="pl_goto"/><Str value="pl_goto"/></HashCell><HashCell><Str value="pl_identification"/><Str value="pl_identification"/></HashCell><HashCell><Str value="pl_interior"/><Str value="Interior"/></HashCell><HashCell><Str value="pl_internalRemark"/><Str value="pl_internalRemark"/></HashCell><HashCell><Str value="pl_letter"/><Str value="pl_letter"/></HashCell><HashCell><Str value="pl_locationDescription"/><Str value="pl_locationDescription"/></HashCell><HashCell><Str value="pl_locationSpec"/><Str value="pl_locationSpec"/></HashCell><HashCell><Str value="pl_location_indication"/><Str value="pl_location_indication"/></HashCell><HashCell><Str value="pl_minimal2input"/><Str value="pl_minimal2input"/></HashCell><HashCell><Str value="pl_minimal3input"/><Str value="pl_minimal3input"/></HashCell><HashCell><Str value="pl_minimalInput"/><Str value="pl_minimalInput"/></HashCell><HashCell><Str value="pl_minimalStreetinput"/><Str value="pl_minimalStreetinput"/></HashCell><HashCell><Str value="pl_notepad"/><Str value="pl_notepad"/></HashCell><HashCell><Str value="pl_number"/><Str value="pl_number"/></HashCell><HashCell><Str value="pl_onStreetGPS"/><Str value="pl_onStreetGPS"/></HashCell><HashCell><Str value="pl_paymentMethod"/><Str value="pl_paymentMethod"/></HashCell><HashCell><Str value="pl_questionnaires"/><Str value="pl_questionnaires"/></HashCell><HashCell><Str value="pl_scanUnit"/><Str value="pl_scanUnit"/></HashCell><HashCell><Str value="pl_tax"/><Str value="pl_tax"/></HashCell><HashCell><Str value="pl_total"/><Str value="pl_total"/></HashCell><HashCell><Str value="pl_towed"/><Str value="pl_towed"/></HashCell><HashCell><Str value="pl_translate"/><Str value="pl_translate"/></HashCell><HashCell><Str value="scanner_app_not_installed"/><Str value="scanner_app_not_installed"/></HashCell><HashCell><Str value="selectDocumentType"/><Str value="Select Document Type"/></HashCell></Hash></Tbl>